#encoding:gbk

account = "*********"  # 实盘交易账户ID

"""
期货策略 - 红白线系统多空双向交易
策略逻辑：
1. 做多：突破5分钟白线或回调到红线支撑位
2. 做空：跌破5分钟红线或回调到白线压力位
3. 止盈止损：5分钟红白线止损，动态止盈
4. 时间过滤：避开收盘前和夜盘收盘前的交易
"""

# ====== 可调整的策略参数 ======
class Config:
    # 开仓参数
    OPEN_LONG_MAX = 3      # 最大开多仓位数
    OPEN_SHORT_MAX = 3     # 最大开空仓位数
    TRADE_HANDS = 1        # 每次交易手数
    SLEEP_TIME = 10        # 开仓间隔时间(分钟)
    
    # 分批止盈设置（ATR倍数方式）
    TAKE_PROFIT_ATR_1 = 1.0   # 第一档止盈ATR倍数
    TAKE_PROFIT_ATR_2 = 1.2   # 第二档止盈ATR倍数（由1.0调大为1.2）
    TAKE_PROFIT_ATR_3 = 1.5   # 第三档止盈ATR倍数（保持1.5）
    PROFIT_PCT_1 = 0.3     # 第一档止盈比例
    PROFIT_PCT_2 = 0.3     # 第二档止盈比例
    PROFIT_PCT_3 = 0.4     # 第三档止盈比例
    
    # 成交量参数
    VOLUME_THRESHOLD_LONG = 1.2  # 做多成交量阈值
    
    # 添加与55均线距离阈值参数
    MA55_DISTANCE_THRESHOLD = 0.004  # 价格与55均线的最大偏离百分比，默认0.5%
    
    # 添加与红白线距离阈值参数
    RED_LINE_DISTANCE_THRESHOLD = 0.004  # 做多时价格与红线最大偏离百分比，默认0.55%
    WHITE_LINE_DISTANCE_THRESHOLD = 0.004  # 做空时价格与白线最大偏离百分比，默认0.55%
    
    # 尾盘清仓时间设置
    DAY_CLOSE_HOUR = 14      # 日盘收盘小时
    DAY_CLOSE_MINUTE = 56    # 日盘清仓分钟
    NIGHT_CLOSE_HOUR = 22    # 夜盘收盘小时
    NIGHT_CLOSE_MINUTE = 56  # 夜盘清仓分钟
    
    # 开盘禁止交易时间设置（分钟）
    DAY_OPEN_NO_TRADE_MINUTES = 5    # 日盘开盘后禁止交易分钟数
    NIGHT_OPEN_NO_TRADE_MINUTES = 5  # 夜盘开盘后禁止交易分钟数
    
    # 添加红白线转换强度参数
    PRICE_STRENGTH_THRESHOLD = 0.0003  # 价格强度阈值（价格高于原白线的最小百分比）
    VOLUME_STRENGTH_THRESHOLD = 1.3   # 成交量强度阈值（相对于前3根K线均值）
    CANDLE_BODY_RATIO = 0.6          # K线实体占比阈值
    
    # 开仓价格优化参数
    LONG_PRICE_DISCOUNT = 0.0003   # 做多时限价单折扣，比当前价低0.1%
    SHORT_PRICE_PREMIUM = 0.0003   # 做空时限价单溢价，比当前价高0.1%
    PRICE_ORDER_TIMEOUT = 3       # 限价单超时K线数，超时后改用市价单
    
    # ===== 新增最大亏损止损参数 =====
    ENABLE_MAX_LOSS_STOP = True  # 是否启用最大亏损止损
    MAX_LOSS_POINTS = 10         # 最大亏损点数（可根据品种调整）
    CONTRACT_MULTIPLIER = 10     # 合约乘数（可根据品种调整）
    ATR_STOP_MULTIPLE = 0.8      # 默认ATR止损倍数
    MIN_STOP_DISTANCE = 5        # 默认最小止损点数保护
    # 新增：品种专属参数映射
    ATR_STOP_MULTIPLE_MAP = {
        'P': 1.0,    # 棕榈油
        'OI': 1.0,   # 菜籽油
        'Y': 1.0,    # 豆油
        'MA': 0.8,   # 甲醇
        'RB': 0.7,   # 螺纹钢
        'BU': 0.8,   # 沥青
        'FU': 0.8,   # 燃料油
        'RM': 1.0,   # 菜粕
        'PG': 1.0,   # 液化气
        'SF': 1.0,   # 硅铁
        'FG': 1.0,   # 玻璃
        'SP': 1.0,   # 纸浆
        'UR': 1.0,   # 尿素
        'AL': 1.0,   # 氧化铝
        'M': 0.8,    # 豆粕
        'EG': 1.0,   # 乙二醇
        'PX': 1.0,   # 对二甲苯
        'SA': 1.0,   # 纯碱/烧碱
        'HC': 0.7,   # 热卷
        'NA': 1.0,   # 烧碱
    }
    MIN_STOP_DISTANCE_MAP = {
        'P': 8,
        'OI': 10,
        'Y': 8,
        'MA': 5,
        'RB': 6,
        'BU': 8,
        'FU': 8,
        'RM': 8,
        'PG': 10,
        'SF': 10,
        'FG': 10,
        'SP': 10,
        'UR': 10,
        'AL': 10,
        'M': 5,
        'EG': 8,
        'PX': 10,
        'SA': 10,
        'HC': 6,
        'NA': 10,
    }
    ATR_WINDOW_5M = 10   # 5分钟ATR计算窗口，推荐值
    # ===== 新增分品种止盈倍数和最小止盈点数 =====
    TAKE_PROFIT_ATR_1_MAP = {
        'P': 1.0,    # 棕榈油
        'OI': 1.0,   # 菜籽油
        'Y': 1.0,    # 豆油
        'MA': 1.2,   # 甲醇
        'RB': 0.8,   # 螺纹钢
        'BU': 1.0,   # 沥青
        'FU': 1.0,   # 燃料油
        'RM': 1.0,   # 菜籽粕
        'PG': 1.0,   # 液化气
        'SF': 1.0,   # 硅铁
        'FG': 1.0,   # 玻璃
        'SP': 1.0,   # 纸浆
        'UR': 1.0,   # 尿素
        'AL': 1.0,   # 氧化铝
        'M': 1.0,    # 豆粕
        'EG': 1.0,   # 乙二醇
        'PX': 1.0,   # 对二甲苯
        'SA': 1.0,   # 纯碱/烧碱
        'HC': 0.8,   # 热卷
        'NA': 1.0,   # 烧碱
    }
    MIN_TAKE_PROFIT_POINTS_MAP = {
        'P': 8,      # 棕榈油
        'OI': 10,    # 菜籽油
        'Y': 8,      # 豆油
        'MA': 10,    # 甲醇
        'RB': 6,     # 螺纹钢
        'BU': 8,     # 沥青
        'FU': 8,     # 燃料油
        'RM': 8,     # 菜籽粕
        'PG': 10,    # 液化气
        'SF': 10,    # 硅铁
        'FG': 10,    # 玻璃
        'SP': 10,    # 纸浆
        'UR': 10,    # 尿素
        'AL': 10,    # 氧化铝
        'M': 5,      # 豆粕
        'EG': 8,     # 乙二醇
        'PX': 10,    # 对二甲苯
        'SA': 10,    # 纯碱/烧碱
        'HC': 6,     # 热卷
        'NA': 10,    # 烧碱
    }
    DEFAULT_TAKE_PROFIT_ATR_1 = 1.0
    DEFAULT_MIN_TAKE_PROFIT_POINTS = 8
    # ===== 新增：止损后是否立即反向开仓参数 =====
    ENABLE_REVERSE_ON_STOP = True  # 止损后是否立即反向开仓，默认True
    # ===== 新增大阴阳线过滤分品种参数 =====
    BIG_BAR_THRESHOLD_MAP = {
        'P': 0.003,   # 棕榈油 0.3%
        'Y': 0.003,   # 豆油 0.3%
        'MA': 0.003,  # 甲醇 0.3%
        'RB': 0.003,  # 螺纹钢 0.3%
        # 可继续添加其他品种
    }
    DEFAULT_BIG_BAR_THRESHOLD = 0.003  # 默认0.3%
    BIG_BAR_LOOKBACK = 3  # 检查前N根K线是否有大阴/大阳线，默认3
    # ===== 新增开仓ATR止损保护期参数 =====
    ATR_STOP_PROTECT_MINUTES = 7  # 开仓后N分钟内不执行ATR止损（单位：分钟）
    # ===== 新增：趋势判断和成交量判断启用参数 =====
    ENABLE_TREND_CHECK = True     # 是否启用趋势判断，默认True
    ENABLE_VOLUME_CHECK = True    # 是否启用成交量判断，默认True
    # ===== 新增：价格与均线距离判断启用参数 =====
    ENABLE_MA55_DISTANCE_CHECK = True  # 是否启用价格与55均线距离判断，默认True
    # ===== 新增：止损判断使用5分钟收盘价参数 =====
    USE_5M_CLOSE_FOR_STOP = True  # 止损判断是否使用5分钟收盘价，默认True
    # ===== 新增红线/白线距离阈值，近距离只用红白线止损 =====
    NO_ATR_STOP_DISTANCE = 0.003  # 价格与红线/白线距离小于0.3%时只用红白线止损

import math
import time
import pandas as pd
import numpy as np

# ====== 新增全局统计变量 ======
trade_stats = {
    'profit_points': [],  # 止盈点数
    'loss_points': [],    # 止损点数
    'profit_amount': [],  # 止盈金额
    'loss_amount': [],    # 止损金额
    'total_trades': 0
}

def log_trade(action, price, profit=None):
    """记录交易信息，并统计止盈止损点数和金额"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
    if profit is not None:
        print(f"[{timestamp}] {action}: 价格={price:.2f}, 收益={profit:.2f}%")
    else:
        print(f"[{timestamp}] {action}: 价格={price:.2f}")
    # ====== 自动统计止盈止损点数和金额 ======
    global trade_stats, g
    if '止盈' in action:
        if hasattr(g, 'open_price') and g.open_price > 0:
            points = abs(price - g.open_price)
            amount = points * getattr(Config, 'CONTRACT_MULTIPLIER', 10) * getattr(g, 'position_size', 1)
            trade_stats['profit_points'].append(points)
            trade_stats['profit_amount'].append(amount)
            trade_stats['total_trades'] += 1
    elif '止损' in action:
        if hasattr(g, 'open_price') and g.open_price > 0:
            points = abs(price - g.open_price)
            amount = points * getattr(Config, 'CONTRACT_MULTIPLIER', 10) * getattr(g, 'position_size', 1)
            trade_stats['loss_points'].append(points)
            trade_stats['loss_amount'].append(amount)
            trade_stats['total_trades'] += 1
    # 每100笔交易输出统计
    if trade_stats['total_trades'] % 100 == 0 and trade_stats['total_trades'] > 0:
        print_trade_stats()

# ====== 新增统计输出函数 ======
def print_trade_stats():
    """输出当前实盘平均止盈、止损点数和盈亏比"""
    global trade_stats
    n_profit = len(trade_stats['profit_points'])
    n_loss = len(trade_stats['loss_points'])
    avg_profit_points = sum(trade_stats['profit_points']) / n_profit if n_profit > 0 else 0
    avg_loss_points = sum(trade_stats['loss_points']) / n_loss if n_loss > 0 else 0
    avg_profit_amount = sum(trade_stats['profit_amount']) / n_profit if n_profit > 0 else 0
    avg_loss_amount = sum(trade_stats['loss_amount']) / n_loss if n_loss > 0 else 0
    win_rate = n_profit / (n_profit + n_loss) if (n_profit + n_loss) > 0 else 0
    rr_ratio = avg_profit_points / avg_loss_points if avg_loss_points > 0 else 0
    print("\n====== 实盘交易统计 ======")
    print(f"总交易数: {trade_stats['total_trades']}")
    print(f"止盈次数: {n_profit}, 止损次数: {n_loss}")
    print(f"平均止盈点数: {avg_profit_points:.2f}, 平均止损点数: {avg_loss_points:.2f}")
    print(f"平均止盈金额: {avg_profit_amount:.2f}, 平均止损金额: {avg_loss_amount:.2f}")
    print(f"胜率: {win_rate*100:.2f}%")
    print(f"盈亏比: {rr_ratio:.2f}")
    print("========================\n")

def calculate_profit(current_price, open_price, is_long=True):
    """
    计算收益率
    
    参数:
    current_price: 当前价格
    open_price: 开仓价格
    is_long: 是否为多头持仓
    
    返回:
    收益率(%)
    """
    if is_long:
        return (current_price/open_price - 1) * 100
    return (open_price/current_price - 1) * 100

class G():
    def __init__(self):
        # 持仓状态
        self.position = "none"  # "none", "long", "short"
        self.hold_price = 0     # 持仓最高/最低价
        self.open_price = 0     # 开仓价格
        
        # 交易计数
        self.buy_long = 0       # 做多次数
        self.buy_short = 0      # 做空次数
        
        # 做多止盈标记
        self.profit_taken_1 = False
        self.profit_taken_2 = False  
        self.profit_taken_3 = False
        
        # 做空止盈标记
        self.profit_taken_1_short = False
        self.profit_taken_2_short = False
        self.profit_taken_3_short = False
        
        # 其他状态
        self.trace_time_long = 0  # 上次做多交易时间
        self.trace_time_short = 0  # 上次做空交易时间
        self.last_close_time = 0   # 上次平仓时间，用于控制反向交易
        self.opened_t = []        # 已开仓的时间点
        self.hold_code = None     # 持仓代码
        self.code = None          # 当前代码
        self.position_size = 0    # 持仓数量记录
        self.sysid = {}           # 订单系统ID
        self.cover = 1            # 尾盘平仓标记
        self.info = None          # 合约信息
        self.open_time = 0  # 新增：记录开仓时间戳
        

# 实例化全局变量g
g = G()

def init(ContextInfo):
    """
    初始化函数 - 在策略启动时执行一次
    
    参数:
    ContextInfo: 上下文信息
    """
    global g  # 声明使用全局变量g
    
    # 设置交易账户
    ContextInfo.set_account(account)
    
    # 获取市场代码
    market = ContextInfo.market
    market = market.replace("SHFE",'SF').replace("CZCE",'ZF').replace("DCE",'DF').replace("CFFEX",'IF')
    
    # 获取合约代码
    code = ContextInfo.stockcode + '.' + ContextInfo.market
    
    # 自动获取主力合约
    g.code = ContextInfo.get_main_contract(code) + '.' + market
    g.market = market
    
    # 获取合约信息
    g.info = ContextInfo.get_instrumentdetail(g.code)
    
    # 初始化尾盘清仓标志
    g.cover = 0
    
    # 初始化上次交易时间，设置为当前时间减去开仓间隔时间，确保策略启动后不会立即开仓
    current_time = time.time()
    max_wait_seconds = 30 * 60  # 与should_reverse_trade函数中保持一致
    g.trace_time_long = current_time - Config.SLEEP_TIME * 30  # 设置为较早的时间，但不会立即触发
    g.trace_time_short = current_time - Config.SLEEP_TIME * 30
    g.last_close_time = current_time - max_wait_seconds - 1  # 确保初始状态下不会立即触发反向交易
    
    # 初始化其他变量
    g.position = "none"  # 当前持仓方向
    g.position_size = 0  # 当前持仓手数
    g.buy_long = 0  # 开多次数
    g.buy_short = 0  # 开空次数
    g.hold_price = 0  # 持仓价格
    g.open_price = 0  # 开仓价格
    g.hold_code = ""  # 持仓合约
    g.opened_t = []  # 已开仓时间
    g.sysid = {}  # 系统ID
    
    # 初始化止盈标记
    g.profit_taken_1 = False
    g.profit_taken_2 = False
    g.profit_taken_3 = False
    g.profit_taken_1_short = False
    g.profit_taken_2_short = False
    g.profit_taken_3_short = False
    
    # 根据品种调整参数
    if "MA" in g.code:  # 甲醇
        g.volume_threshold = Config.VOLUME_THRESHOLD_LONG
        g.ma_period = 55
        print(f"{g.code} 红白线系统止损策略初始化完成 - 甲醇品种")
    elif "p" in g.code:  # 棕榈
        g.volume_threshold = Config.VOLUME_THRESHOLD_LONG * 1.2  # 棕榈品种成交量阈值调高20%
        g.ma_period = 55
        print(f"{g.code} 红白线系统止损策略初始化完成 - 棕榈品种")
    else:
        g.volume_threshold = Config.VOLUME_THRESHOLD_LONG
        g.ma_period = 55
        print(f"{g.code} 红白线系统止损策略初始化完成")
    
    # 输出初始化参数
    print(f"策略参数: 成交量阈值={g.volume_threshold}, MA周期={g.ma_period}")
    
    # ====== 自动适配不同品种的止损参数 ======
    code = ContextInfo.stockcode.upper()
    # 用户可在Config中手动设置参数，若未设置则自动匹配
    if not hasattr(Config, 'MAX_LOSS_POINTS') or Config.MAX_LOSS_POINTS is None:
        if code.startswith('P'):
            Config.MAX_LOSS_POINTS = 15
        elif code.startswith('Y'):
            Config.MAX_LOSS_POINTS = 20
        elif code.startswith('MA'):
            Config.MAX_LOSS_POINTS = 30
        else:
            Config.MAX_LOSS_POINTS = 15  # 默认
    # ====== 合约乘数自动适配 ======
    if not hasattr(Config, 'CONTRACT_MULTIPLIER') or Config.CONTRACT_MULTIPLIER is None:
        if code.startswith('P'):
            Config.CONTRACT_MULTIPLIER = 10  # 棕榈油
        elif code.startswith('Y'):
            Config.CONTRACT_MULTIPLIER = 10  # 豆油
        elif code.startswith('MA'):
            Config.CONTRACT_MULTIPLIER = 10  # 甲醇
        elif code.startswith('M'):
            Config.CONTRACT_MULTIPLIER = 10  # 豆粕
        elif code.startswith('SR'):
            Config.CONTRACT_MULTIPLIER = 10  # 白糖
        elif code.startswith('TA'):
            Config.CONTRACT_MULTIPLIER = 5   # PTA
        elif code.startswith('CF'):
            Config.CONTRACT_MULTIPLIER = 5   # 棉花
        elif code.startswith('RU'):
            Config.CONTRACT_MULTIPLIER = 10  # 橡胶
        elif code.startswith('NI'):
            Config.CONTRACT_MULTIPLIER = 1   # 镍
        elif code.startswith('AG'):
            Config.CONTRACT_MULTIPLIER = 15  # 白银
        elif code.startswith('AU'):
            Config.CONTRACT_MULTIPLIER = 1000 # 黄金
        elif code.startswith('I'):
            Config.CONTRACT_MULTIPLIER = 100 # 铁矿石
        elif code.startswith('RB'):
            Config.CONTRACT_MULTIPLIER = 10  # 螺纹钢
        elif code.startswith('HC'):
            Config.CONTRACT_MULTIPLIER = 10  # 热卷
        elif code.startswith('J'):
            Config.CONTRACT_MULTIPLIER = 100 # 焦炭
        elif code.startswith('JM'):
            Config.CONTRACT_MULTIPLIER = 60  # 焦煤
        elif code.startswith('ZC'):
            Config.CONTRACT_MULTIPLIER = 100 # 动力煤
        elif code.startswith('AP'):
            Config.CONTRACT_MULTIPLIER = 10  # 苹果
        elif code.startswith('C'):
            Config.CONTRACT_MULTIPLIER = 10  # 玉米
        elif code.startswith('A'):
            Config.CONTRACT_MULTIPLIER = 10  # 豆一
        elif code.startswith('B'):
            Config.CONTRACT_MULTIPLIER = 10  # 豆二
        elif code.startswith('L'):
            Config.CONTRACT_MULTIPLIER = 5   # 塑料
        elif code.startswith('V'):
            Config.CONTRACT_MULTIPLIER = 5   # PVC
        elif code.startswith('PP'):
            Config.CONTRACT_MULTIPLIER = 5   # 聚丙烯
        elif code.startswith('EG'):
            Config.CONTRACT_MULTIPLIER = 10  # 乙二醇
        elif code.startswith('SC'):
            Config.CONTRACT_MULTIPLIER = 1000 # 原油
        elif code.startswith('BU'):
            Config.CONTRACT_MULTIPLIER = 10  # 沥青
        elif code.startswith('FU'):
            Config.CONTRACT_MULTIPLIER = 10  # 燃油
        elif code.startswith('SP'):
            Config.CONTRACT_MULTIPLIER = 10  # 纸浆
        elif code.startswith('SA'):
            Config.CONTRACT_MULTIPLIER = 20  # 纯碱
        elif code.startswith('UR'):
            Config.CONTRACT_MULTIPLIER = 20  # 尿素
        elif code.startswith('LH'):
            Config.CONTRACT_MULTIPLIER = 16  # 生猪
        else:
            Config.CONTRACT_MULTIPLIER = 10  # 默认
    # ATR止损倍数自动适配
    if not hasattr(Config, 'ATR_STOP_MULTIPLE') or Config.ATR_STOP_MULTIPLE is None:
        if code.startswith('P'):
            Config.ATR_STOP_MULTIPLE = 1.0
        elif code.startswith('Y'):
            Config.ATR_STOP_MULTIPLE = 1.2
        elif code.startswith('MA'):
            Config.ATR_STOP_MULTIPLE = 1.5
        else:
            Config.ATR_STOP_MULTIPLE = 1.0  # 默认
    print(f"自动适配止损参数: MAX_LOSS_POINTS={Config.MAX_LOSS_POINTS}, CONTRACT_MULTIPLIER={Config.CONTRACT_MULTIPLIER}, ATR_STOP_MULTIPLE={Config.ATR_STOP_MULTIPLE}")

def calculate_lines(price_series):
    """
    计算红白线
    
    参数:
    price_series: 价格序列
    
    返回:
    (white_line, red_line): 白线和红线序列
    """
    try:
        # 原有的计算逻辑
        length = len(price_series)
        white_line = pd.Series([None] * length, index=price_series.index)
        red_line = pd.Series([None] * length, index=price_series.index)
        
        last_white = None
        last_red = None
        
        for i in range(length):
            # 如果既没有白线也没有红线，初始化
            if last_white is None and last_red is None:
                # 根据第一个价格的位置确定初始线
                if i > 0:
                    if price_series.iloc[i] > price_series.iloc[i-1]:
                        last_red = price_series.iloc[i-1]  # 上涨，设置红线
                        red_line.iloc[i] = last_red
                        white_line.iloc[i] = None
                    else:
                        last_white = price_series.iloc[i-1]  # 下跌，设置白线
                        white_line.iloc[i] = last_white
                        red_line.iloc[i] = None
                else:
                    # 第一个价格，默认设置为白线
                    last_white = price_series.iloc[i]
                    white_line.iloc[i] = last_white
                    red_line.iloc[i] = None
            
            # 如果有白线，检查是否突破
            elif last_white is not None:
                if price_series.iloc[i] > last_white:  # 突破白线
                    white_line.iloc[i] = None
                    red_line.iloc[i] = last_white  # 白线转为红线
                    last_red = last_white
                    last_white = None
                else:  # 未突破，保持白线
                    white_line.iloc[i] = last_white
                    red_line.iloc[i] = None
                    
            # 如果有红线，检查是否跌破
            elif last_red is not None:
                if price_series.iloc[i] < last_red:  # 跌破红线
                    red_line.iloc[i] = None
                    white_line.iloc[i] = last_red  # 红线转为白线
                    last_white = last_red
                    last_red = None
                else:  # 未跌破，保持红线
                    red_line.iloc[i] = last_red
                    white_line.iloc[i] = None
        
        return white_line, red_line
    except Exception as e:
        print(f"计算红白线出错: {e}")
        return pd.Series([None] * len(price_series)), pd.Series([None] * len(price_series))

def calculate_ma(close, volume, period):
    """
    计算成交量加权均线
    
    参数:
    close: 收盘价序列
    volume: 成交量序列
    period: 周期
    
    返回:
    ma: 成交量加权均线
    """
    weighted_price = close * volume
    ma = weighted_price.rolling(window=period).sum() / volume.rolling(window=period).sum()
    return ma



def handlebar(ContextInfo):
    """主策略函数"""
    global g
    
    # 如果不是最后一根K线，直接返回
    if not ContextInfo.is_last_bar():
        return
    
    # 同步持仓状态
    sync_position_status(ContextInfo)
    
    # 获取时间和价格数据
    timetag = ContextInfo.get_bar_timetag(ContextInfo.barpos)
    try:
        t = time.strftime("%H%M%S", time.localtime(timetag))
    except (TypeError, ValueError, OSError):
        t = "000000"
    
    # ====== 用系统当前时间严格过滤开盘5分钟 ======
    now_struct = time.localtime()
    sys_hour = now_struct.tm_hour
    sys_minute = now_struct.tm_min
    is_day_open_no_trade = sys_hour == 9 and sys_minute < Config.DAY_OPEN_NO_TRADE_MINUTES
    is_night_open_no_trade = sys_hour == 21 and sys_minute < Config.NIGHT_OPEN_NO_TRADE_MINUTES
    # ====== 系统时间过滤结束 ======
    
    # ====== 收盘前10分钟禁止开新仓位 ======
    is_day_close_no_trade = (sys_hour == 14 and sys_minute >= 50)
    is_night_close_no_trade = (sys_hour == 22 and sys_minute >= 50)
    # ====== 收盘前10分钟禁止开新仓位结束 ======
    
    # ====== 日盘/夜盘开盘5分钟过滤日志输出 ======
    print(f"【时间过滤】当前系统时间: {sys_hour:02d}:{sys_minute:02d}")
    if is_day_open_no_trade or is_night_open_no_trade:
        print("【时间过滤】当前属于禁止开仓时间段（9:00-9:05 或 21:00-21:05），不允许开仓！")
    elif is_day_close_no_trade or is_night_close_no_trade:
        print("【时间过滤】当前属于收盘前10分钟禁止开新仓位时间段（14:50-15:00 或 22:50-23:00），不允许开新仓！")
    else:
        print("【时间过滤】当前属于允许开仓时间段，可以正常开仓。\n（9:00-9:05、21:00-21:05为禁止开仓时间段，14:50-15:00、22:50-23:00为禁止开新仓时间段）")
    # ====== 日盘/夜盘开盘5分钟过滤日志输出结束 ======
    
    timetag = ContextInfo.get_bar_timetag(ContextInfo.barpos)
    bar_date = timetag_to_datetime(timetag, '%Y%m%d%H%M%S')
    start, end = after_init(ContextInfo)
    price = ContextInfo.get_market_data(['close','amount','volume','high','low'],[g.code],period='1m',
                    start_time=start+'200000',
                    end_time=bar_date,
                    )
    price_1d = ContextInfo.get_market_data(['close','open'],[g.code],period='1d',
                    start_time=start,
                    end_time=start,
                    count=1)
    # 减少调试信息输出
    # print('price_1d',price_1d)
    #price.to_csv('d:/ma.csv')
    price_len = price.shape[0]
    C = price['close']
    # 获取当前时间（用于其他逻辑）
    hour = int(t[:2])
    minute = int(t[2:4])
    
    # 注意：时间过滤条件已经在上面使用系统时间定义了
    # 这里不再重复定义，避免覆盖系统时间过滤变量
    
    # 定义b1时间过滤条件（使用系统时间过滤变量）
    b1 = not ((sys_hour == Config.DAY_CLOSE_HOUR and sys_minute >= Config.DAY_CLOSE_MINUTE) or 
              (sys_hour == Config.NIGHT_CLOSE_HOUR and sys_minute >= Config.NIGHT_CLOSE_MINUTE) or
              is_day_open_no_trade or 
              is_night_open_no_trade)
    
    # 检查是否需要尾盘清仓（使用系统时间）
    if ((sys_hour == Config.DAY_CLOSE_HOUR and sys_minute >= Config.DAY_CLOSE_MINUTE) or 
        (sys_hour == Config.NIGHT_CLOSE_HOUR and sys_minute >= Config.NIGHT_CLOSE_MINUTE)):
        # 设置尾盘清仓标志
        g.cover = 1
        
        # 如果有持仓，执行尾盘清仓
        if g.position != "none" and g.position_size > 0:
            if g.position == "long":
                passorder(7, 1101, account, g.code, 14, 0, g.position_size, '', 1, '尾盘平多', ContextInfo)
                log_trade("尾盘平多", 0)
                g.trace_time_long = time.time()  # 平多时重置开多计时
            elif g.position == "short":
                passorder(9, 1101, account, g.code, 14, 0, g.position_size, '', 1, '尾盘平空', ContextInfo)
                log_trade("尾盘平空", 0)
                g.trace_time_short = time.time()  # 平空时重置开空计时
            
            g.position = "none"
            g.position_size = 0
            sync_position_status(ContextInfo)
            return
    else:
        g.cover = 0
    
    try:
        # 获取K线数据 - 使用5分钟周期
        price_5m = ContextInfo.get_market_data(['open', 'high', 'low', 'close', 'volume'], 
                                          [g.code], 
                                          period='5m',
                                          count=60)
        
        # 获取当前价格
        current_price = price_5m['close'].iloc[-1]
        
        # 使用文华指标源代码计算红白线
        white_line_5m, red_line_5m = calculate_red_white_lines_exact(price_5m)
        
        # 计算30分钟红白线
        price_30m = ContextInfo.get_market_data(['open', 'high', 'low', 'close', 'volume'], 
                                            [g.code], 
                                            period='30m',
                                            count=60)
        white_line_30m, red_line_30m = calculate_red_white_lines_exact(price_30m)
        
        # 计算成交量加权均线
        MA_Line = calculate_ma(price_5m['close'], price_5m['volume'], 55)
        
        # 初始化交易信号变量
        break_white_long = False    # 突破白线做多
        break_red_short = False     # 跌破红线做空
        pullback_long_good = False  # 回调到红线做多
        pullback_short_good = False # 反弹到白线做空
        red_support_long = False    # 红线支撑做多
        
        # 检查是否跌破红线
        break_red = check_break_red_line(price_5m, 
                                       red_line_5m.iloc[-2] if len(red_line_5m) > 1 else None, 
                                       red_line_5m.iloc[-1], current_price)
        
        # 检查是否突破白线
        if white_line_5m.iloc[-1] is not None and not pd.isna(white_line_5m.iloc[-1]):
            if current_price > white_line_5m.iloc[-1]:
                break_white_long = True
                # print(f"检测到突破白线: 价格{current_price} > 白线{white_line_5m.iloc[-1]}")
        
        # 检查是否回调到红线做多
        if red_line_5m.iloc[-1] is not None and not pd.isna(red_line_5m.iloc[-1]):
            if price_5m['low'].iloc[-1] <= red_line_5m.iloc[-1] and current_price > red_line_5m.iloc[-1]:
                pullback_long_good = True
                # print(f"检测到回调到红线做多: 最低价{price_5m['low'].iloc[-1]} <= 红线{red_line_5m.iloc[-1]}, 收盘价{current_price} > 红线{red_line_5m.iloc[-1]}")
        
        # 检查是否反弹到白线做空
        if white_line_5m.iloc[-1] is not None and not pd.isna(white_line_5m.iloc[-1]):
            # ===== 新增：大阳线过滤（分品种自动调整） =====
            code_prefix = g.code[:2].upper() if hasattr(g, 'code') and g.code else ''
            threshold = Config.BIG_BAR_THRESHOLD_MAP.get(code_prefix, Config.DEFAULT_BIG_BAR_THRESHOLD)
            lookback = Config.BIG_BAR_LOOKBACK
            def is_big_bullish_kline(open_price, close_price, threshold=threshold):
                return (close_price - open_price) / open_price > threshold
            lastN_open = price_5m['open'].iloc[-(lookback+1):-1]
            lastN_close = price_5m['close'].iloc[-(lookback+1):-1]
            big_bullish = any(is_big_bullish_kline(o, c) for o, c in zip(lastN_open, lastN_close))
            if big_bullish:
                print(f"前{lookback}根K线有大阳线（阈值{threshold*100:.2f}%），白线压力做空信号无效，跳过")
            elif price_5m['high'].iloc[-1] >= white_line_5m.iloc[-1] and current_price < white_line_5m.iloc[-1]:
                pullback_short_good = True
                # print(f"检测到反弹到白线做空: 最高价{price_5m['high'].iloc[-1]} >= 白线{white_line_5m.iloc[-1]}, 收盘价{current_price} < 白线{white_line_5m.iloc[-1]}")
        
        # 修改红线支撑做多条件，增加转换期保护和大阴线过滤
        if red_line_5m.iloc[-1] is not None and not pd.isna(red_line_5m.iloc[-1]):
            # 检查是否处于转换期（前一根是白线，当前是红线）
            is_transition_period = (white_line_5m.iloc[-2] is not None and not pd.isna(white_line_5m.iloc[-2]) and
                                  white_line_5m.iloc[-1] is None and
                                  red_line_5m.iloc[-1] is not None)
            # ===== 新增：大阴线过滤（分品种自动调整） =====
            code_prefix = g.code[:2].upper() if hasattr(g, 'code') and g.code else ''
            threshold = Config.BIG_BAR_THRESHOLD_MAP.get(code_prefix, Config.DEFAULT_BIG_BAR_THRESHOLD)
            lookback = Config.BIG_BAR_LOOKBACK
            def is_big_bearish_kline(open_price, close_price, threshold=threshold):
                return (open_price - close_price) / open_price > threshold
            lastN_open = price_5m['open'].iloc[-(lookback+1):-1]
            lastN_close = price_5m['close'].iloc[-(lookback+1):-1]
            big_bearish = any(is_big_bearish_kline(o, c) for o, c in zip(lastN_open, lastN_close))
            if big_bearish:
                print(f"前{lookback}根K线有大阴线（阈值{threshold*100:.2f}%），红线支撑做多信号无效，跳过")
            else:
                volume_ok = not Config.ENABLE_VOLUME_CHECK or check_volume_increase(price_5m['volume'])
                if current_price > red_line_5m.iloc[-1] and volume_ok:
                    if not is_transition_period:
                        # 只有在非转换期且无大阴线时才考虑红线支撑做多
                        red_support_long = True
                        # print(f"检测到红线支撑做多: 价格{current_price} > 红线{red_line_5m.iloc[-1]}")
                    else:
                        pass  # 保证 else 代码块不为空，防止缩进错误
        
        # 检测红白线转换
        transition_signal = detect_line_transition(
            white_line_5m.iloc[-2] if len(white_line_5m) > 1 else None,
            white_line_5m.iloc[-1],
            red_line_5m.iloc[-2] if len(red_line_5m) > 1 else None,
            red_line_5m.iloc[-1]
        )
        
        # 打印当前5分钟红白线值（如果有）
        white_val = white_line_5m.iloc[-1] if white_line_5m.iloc[-1] is not None and not pd.isna(white_line_5m.iloc[-1]) else '-'
        red_val = red_line_5m.iloc[-1] if red_line_5m.iloc[-1] is not None and not pd.isna(red_line_5m.iloc[-1]) else '-'
        print(f"5分钟红白线: 白线: {white_val} | 红线: {red_val}")
        
        # 计算趋势
        current_trend = check_trend(price_5m, MA_Line.iloc[-1])
        
        # 获取转换信号的交易建议
        transition_long, transition_short = should_trade_with_transition(
            transition_signal,
            current_price,
            MA_Line.iloc[-1],
            current_trend,
            price_5m['volume'],
            white_line_5m.iloc[-1],
            red_line_5m.iloc[-1],
            b1,  # 时间过滤参数
            MA_Line.iloc[-1],  # 55均线值
            white_line_5m.iloc[-2] if len(white_line_5m) > 1 else None,
            red_line_5m.iloc[-2] if len(red_line_5m) > 1 else None
        )
        
        # 修改开仓条件，增加转换期保护
        trend_ok = not Config.ENABLE_TREND_CHECK or check_trend(price_5m, MA_Line.iloc[-1]) == 1
        kaichang = ((break_white_long or pullback_long_good or 
                   (red_support_long and not transition_signal == 1) or  # 红线支撑做多，但不在白转红期间
                   transition_long) and 
                   current_price > MA_Line.iloc[-1] and 
                   g.buy_long < Config.OPEN_LONG_MAX and 
                   b1 and 
                   trend_ok) and C[-1]>(price_1d.iloc[0]['close']+price_1d.iloc[0]['open'])/2
        
        trend_ok_short = not Config.ENABLE_TREND_CHECK or check_trend(price_5m, MA_Line.iloc[-1]) == -1
        kaikong = ((break_red_short or pullback_short_good or transition_short) and 
                  current_price < MA_Line.iloc[-1] and 
                  g.buy_short < Config.OPEN_SHORT_MAX and 
                  b1 and 
                  trend_ok_short and
                  (red_line_5m.iloc[-1] is None or current_price < red_line_5m.iloc[-1]))  and C[-1]<(price_1d.iloc[0]['close']+price_1d.iloc[0]['open'])/2 # 新增条件
        
        # 是否可以开仓
        can_open_position = True  # 默认可以开仓
        
        # 增加价格与红白线距离的过滤条件（减少调试输出）
        red_line_distance_ok, white_line_distance_ok = check_price_line_distance(current_price, white_line_5m.iloc[-1], red_line_5m.iloc[-1])
        
        # 根据距离检查结果修改开仓条件
        if (break_white_long or pullback_long_good or (red_support_long and not transition_signal == 1) or transition_long) and not red_line_distance_ok:
            print("因价格与红线距离过大，取消做多信号")
            kaichang = False
            
        if (break_red_short or pullback_short_good or transition_short) and not white_line_distance_ok:
            print("因价格与白线距离过大，取消做空信号")
            kaikong = False
        
        # 减少调试信息输出
        atr = calculate_atr(ContextInfo)
        print(f"【信号阶段ATR】当前ATR值: {atr:.2f}")
        
        # 开仓逻辑
        if g.position == "none":
            # 获取当前K线时间戳，用于计算开仓间隔
            current_bar_time = timetag if isinstance(timetag, (int, float)) else time.time()
            
            # 开多仓 - 添加开盘禁止交易条件
            if kaichang and can_open_position and not is_day_open_no_trade and not is_night_open_no_trade and not is_day_close_no_trade and not is_night_close_no_trade and (current_bar_time - g.trace_time_long) > Config.SLEEP_TIME*60 and t not in g.opened_t:
                # 确定仓位级别
                g.position_level = determine_position_level(ContextInfo, g.code, current_price)
                
                # 检查是否为红白线转换信号
                if transition_long:
                    # 使用限价单，设置比当前价格低的入场价
                    order_price = current_price * (1 - Config.LONG_PRICE_DISCOUNT)
                    print(f"红白线转换做多信号: 使用限价单 价格={order_price:.2f} (当前价格{current_price:.2f}的{Config.LONG_PRICE_DISCOUNT*100:.2f}%折扣)")
                    # 使用限价单 (价格类型12为限价单，14为市价单)
                    passorder(0, 1101, account, g.code, 12, order_price, Config.TRADE_HANDS, '', 1, '期货策略限价开多', ContextInfo)
                else:
                    # 其他信号使用市价单
                    passorder(0, 1101, account, g.code, 14, 0, Config.TRADE_HANDS, '', 1, '期货策略开多', ContextInfo)
                
                # 更新状态变量
                g.position = "long"
                g.buy_long += 1
                g.trace_time_long = current_bar_time
                g.hold_price = current_price
                g.open_price = current_price
                g.opened_t.append(t)
                g.hold_code = g.code
                g.position_size = Config.TRADE_HANDS
                g.sysid = {}
                
                # 重置止盈标记
                g.profit_taken_1 = False
                g.profit_taken_2 = False
                g.profit_taken_3 = False
                
                log_trade("开多" + ("(限价单)" if transition_long else ""), current_price)
            
            # 开空仓 - 添加开盘禁止交易条件
            elif kaikong and can_open_position and not is_day_open_no_trade and not is_night_open_no_trade and not is_day_close_no_trade and not is_night_close_no_trade and (current_bar_time - g.trace_time_short) > Config.SLEEP_TIME*60 and t not in g.opened_t:
                # 确定仓位级别
                g.position_level = determine_position_level(ContextInfo, g.code, current_price)
                
                # 检查是否为红白线转换信号
                if transition_short:
                    # 使用限价单，设置比当前价格高的入场价
                    order_price = current_price * (1 + Config.SHORT_PRICE_PREMIUM)
                    print(f"红白线转换做空信号: 使用限价单 价格={order_price:.2f} (当前价格{current_price:.2f}的{Config.SHORT_PRICE_PREMIUM*100:.2f}%溢价)")
                    # 使用限价单
                    passorder(3, 1101, account, g.code, 12, order_price, Config.TRADE_HANDS, '', 1, '期货策略限价开空', ContextInfo)
                else:
                    # 其他信号使用市价单
                    passorder(3, 1101, account, g.code, 14, 0, Config.TRADE_HANDS, '', 1, '期货策略开空', ContextInfo)
                
                # 更新状态变量
                g.position = "short"
                g.buy_short += 1
                g.trace_time_short = current_bar_time
                g.hold_price = current_price
                g.open_price = current_price
                g.opened_t.append(t)
                g.hold_code = g.code
                g.position_size = Config.TRADE_HANDS
                g.sysid = {}
                
                # 重置止盈标记
                g.profit_taken_1_short = False
                g.profit_taken_2_short = False
                g.profit_taken_3_short = False
                
                log_trade("开空" + ("(限价单)" if transition_short else ""), current_price)
        
        # 持仓管理
        elif g.position == "long":
            # 止盈逻辑
            executed_take_profit = execute_take_profit(ContextInfo, current_price)
            
            # 如果没有执行止盈，检查止损条件
            if not executed_take_profit:
                # 检查是否跌破红线
                # 根据配置决定使用即时价格还是5分钟收盘价进行止损判断
                if Config.USE_5M_CLOSE_FOR_STOP:
                    # 使用5分钟收盘价判断
                    price_for_stop_check = price_5m['close'].iloc[-1]
                else:
                    # 使用即时价格判断
                    price_for_stop_check = current_price
                
                if break_red and price_for_stop_check < red_line_5m.iloc[-1]:
                    # 平多仓
                    last_position_type = g.position
                    passorder(7, 1101, account, g.code, 14, 0, g.position_size, '', 1, '多单止损', ContextInfo)
                    log_trade("多单止损", current_price)
                    g.position = "none"
                    g.position_size = 0
                    sync_position_status(ContextInfo)
                    g.last_close_time = time.time()
                    # 修复：止损后立即反向开仓（可选）
                    if Config.ENABLE_REVERSE_ON_STOP:
                        check_stop_loss_and_reverse(ContextInfo, current_price, last_position_type)
                    g.trace_time_long = time.time()  # 止损平多时重置开多计时
                else:
                    # ===== 新增：ATR止损和最大亏损止损判断 =====
                    should_stop, stop_price, stop_reason = enhanced_stop_loss_check(ContextInfo, current_price)
                    if should_stop:
                        last_position_type = g.position
                        passorder(7, 1101, account, g.code, 14, 0, g.position_size, '', 1, '多单ATR/最大亏损止损', ContextInfo)
                        log_trade(f"多单{stop_reason}", current_price)
                        g.position = "none"
                        g.position_size = 0
                        sync_position_status(ContextInfo)
                        g.last_close_time = time.time()
                        # 修复：止损后立即反向开仓（可选）
                        if Config.ENABLE_REVERSE_ON_STOP:
                            check_stop_loss_and_reverse(ContextInfo, current_price, last_position_type)
                        g.trace_time_long = time.time()  # ATR/最大亏损止损平多时重置开多计时
        
        elif g.position == "short":
            # 止盈逻辑
            executed_take_profit = execute_take_profit(ContextInfo, current_price)
            
            # 检查是否突破白线（空头止损用）
            first_break_white = False
            if len(white_line_5m) > 1 and white_line_5m.iloc[-2] is not None and not pd.isna(white_line_5m.iloc[-2]):
                if (white_line_5m.iloc[-1] is None or pd.isna(white_line_5m.iloc[-1])) and current_price > white_line_5m.iloc[-2]:
                    first_break_white = True
            
            # 如果没有执行止盈，检查止损条件
            if not executed_take_profit:
                # 检查是否突破白线
                if first_break_white:
                    # 平空仓
                    last_position_type = g.position
                    passorder(9, 1101, account, g.code, 14, 0, g.position_size, '', 1, '空单止损', ContextInfo)
                    log_trade("空单止损", current_price)
                    g.position = "none"
                    g.position_size = 0
                    sync_position_status(ContextInfo)
                    g.last_close_time = time.time()
                    # 修复：止损后立即反向开仓（可选）
                    if Config.ENABLE_REVERSE_ON_STOP:
                        check_stop_loss_and_reverse(ContextInfo, current_price, last_position_type)
                else:
                    # ===== 新增：ATR止损和最大亏损止损判断（空单） =====
                    should_stop, stop_price, stop_reason = enhanced_stop_loss_check(ContextInfo, current_price)
                    if should_stop:
                        last_position_type = g.position
                        passorder(9, 1101, account, g.code, 14, 0, g.position_size, '', 1, '空单ATR/最大亏损止损', ContextInfo)
                        log_trade(f"空单{stop_reason}", current_price)
                        g.position = "none"
                        g.position_size = 0
                        sync_position_status(ContextInfo)
                        g.last_close_time = time.time()
                        # 修复：止损后立即反向开仓（可选）
                        if Config.ENABLE_REVERSE_ON_STOP:
                            check_stop_loss_and_reverse(ContextInfo, current_price, last_position_type)
                        g.trace_time_short = time.time()  # 止损平空时重置开空计时
    
    except Exception as e:
        print(f"处理K线数据出错: {e}")
        return
    
    # 检查止损和反向开仓 - 只要有持仓就执行检查
    if g.position != "none":
        check_stop_loss_and_reverse(ContextInfo, current_price)

    # ====== 减少持仓信息日志输出 ======
    if g.position != "none" and g.open_price > 0:
        try:
            current_price = ContextInfo.get_market_data(['close'], [g.code], period='1m', count=1)['close'].iloc[-1]
            code = g.code if hasattr(g, 'code') else ''
            atr_stop_multiple = get_atr_stop_multiple(code)
            min_stop_distance = get_min_stop_distance(code)
            atr = calculate_atr(ContextInfo)
            atr_stop_price = g.open_price - atr * atr_stop_multiple if g.position == "long" else g.open_price + atr * atr_stop_multiple
            min_stop_price = g.open_price - min_stop_distance if g.position == "long" else g.open_price + min_stop_distance
            final_stop_price = min(atr_stop_price, min_stop_price) if g.position == "long" else max(atr_stop_price, min_stop_price)
            max_loss_price = g.open_price - getattr(Config, 'MAX_LOSS_POINTS', 15) if g.position == "long" else g.open_price + getattr(Config, 'MAX_LOSS_POINTS', 15)
            profit_pct = calculate_profit(current_price, g.open_price, g.position == "long")
            # 分批止盈价
            open_atr = getattr(g, 'open_atr', None)
            if open_atr is None:
                open_atr = calculate_atr(ContextInfo)
                g.open_atr = open_atr
            tp1 = g.open_price + open_atr * Config.TAKE_PROFIT_ATR_1 if g.position == "long" else g.open_price - open_atr * Config.TAKE_PROFIT_ATR_1
            tp2 = g.open_price + open_atr * Config.TAKE_PROFIT_ATR_2 if g.position == "long" else g.open_price - open_atr * Config.TAKE_PROFIT_ATR_2
            tp3 = g.open_price + open_atr * Config.TAKE_PROFIT_ATR_3 if g.position == "long" else g.open_price - open_atr * Config.TAKE_PROFIT_ATR_3
            # 新增：持仓时间打印
            hold_minutes = 0.0
            if hasattr(g, 'open_time') and g.open_time > 0:
                hold_minutes = (time.time() - g.open_time) / 60
            print("\n====== 持仓详情 ======")
            print(f"持仓方向: {g.position}")
            print(f"持仓价: {g.open_price}")
            print(f"当前价: {current_price}")
            print(f"当前盈亏: {profit_pct:.2f}%")
            print(f"持仓时间: {hold_minutes:.2f} 分钟")
            print(f"ATR止损倍数: {atr_stop_multiple}, 最小止损点数: {min_stop_distance}")
            print(f"ATR止损价: {atr_stop_price:.2f}, 最小止损保护价: {min_stop_price:.2f}, 实际止损价: {final_stop_price:.2f}, 最大亏损止损价: {max_loss_price:.2f}")
            print(f"分批止盈价: 第一档: {tp1:.2f}, 第二档: {tp2:.2f}, 第三档: {tp3:.2f}")
            print("====================\n")
        except Exception as e:
            print(f"持仓信息日志输出出错: {e}")
    # ====== 持仓信息日志输出优化结束 ======

def check_stop_loss(ContextInfo, current_price):
    """检查是否触发止损 - 简化版，仅使用红白线系统"""
    # 获取当前时间
    timetag = ContextInfo.get_bar_timetag(ContextInfo.barpos)
    try:
        t = time.strftime("%H%M%S", time.localtime(timetag))
    except (TypeError, ValueError, OSError):
        if isinstance(timetag, str):
            t = timetag[-6:] if len(timetag) >= 6 else "000000"
        else:
            t = time.strftime("%H%M%S")
    
    # 获取5分钟K线数据
    try:
        price_5m = ContextInfo.get_market_data(['open', 'high', 'low', 'close', 'volume'], 
                                           [g.code], 
                                           period='5m',
                                           count=10)
        
        # 使用文华指标源代码计算红白线
        white_line_5m, red_line_5m = calculate_red_white_lines_exact(price_5m)
    except Exception as e:
        print(f"获取5分钟K线数据出错: {str(e)}")
        return False
    
    if g.position == "long":
        # 做多止损条件：5分钟K线收盘价跌破5分钟红色支撑线
        red_line_broken = False
        
        # 检查是否跌破红线 - 情况1：红线消失
        if red_line_5m.iloc[-2] is not None and not pd.isna(red_line_5m.iloc[-2]) and (red_line_5m.iloc[-1] is None or pd.isna(red_line_5m.iloc[-1])):
            if price_5m['close'].iloc[-1] < red_line_5m.iloc[-2]:
                red_line_broken = True
                print("多单止损信号：检测到跌破红线支撑（红线消失）")
        
        # 检查是否跌破红线 - 情况2：价格低于红线
        elif red_line_5m.iloc[-1] is not None and not pd.isna(red_line_5m.iloc[-1]):
            # 根据配置决定使用即时价格还是5分钟收盘价进行止损判断
            if Config.USE_5M_CLOSE_FOR_STOP:
                # 使用5分钟收盘价判断
                price_for_stop_check = price_5m['close'].iloc[-1]
            else:
                # 使用即时价格判断
                price_for_stop_check = current_price
                
            if price_for_stop_check < red_line_5m.iloc[-1]:
                red_line_broken = True
                print(f"多单止损信号：检测到跌破红线支撑（价格{price_for_stop_check}低于红线{red_line_5m.iloc[-1]}）")
        
        if red_line_broken:
            # 计算当前盈亏
            profit_pct = calculate_profit(current_price, g.open_price, True)
            
            # 执行止损
            passorder(7, 1101, account, g.code, 14, 0, g.position_size, '', 1, '多单红线止损', ContextInfo)
            log_trade("多单红线止损", current_price, profit_pct)
            g.position = "none"
            g.position_size = 0
            sync_position_status(ContextInfo)
            g.trace_time_long = time.time()  # 红线止损平多时重置开多计时
            return True
    
    elif g.position == "short":
        # 做空止损条件：5分钟K线收盘价突破白色压力线
        white_line_broken = False
        
        # 检查是否突破白线 - 情况1：白线消失
        if white_line_5m.iloc[-2] is not None and not pd.isna(white_line_5m.iloc[-2]) and (white_line_5m.iloc[-1] is None or pd.isna(white_line_5m.iloc[-1])):
            if price_5m['close'].iloc[-1] > white_line_5m.iloc[-2]:
                white_line_broken = True
                print("空单止损信号：检测到突破白线压力（白线消失）")
        
        # 检查是否突破白线 - 情况2：价格高于白线
        elif white_line_5m.iloc[-1] is not None and not pd.isna(white_line_5m.iloc[-1]):
            # 根据配置决定使用即时价格还是5分钟收盘价进行止损判断
            if Config.USE_5M_CLOSE_FOR_STOP:
                # 使用5分钟收盘价判断
                price_for_stop_check = price_5m['close'].iloc[-1]
            else:
                # 使用即时价格判断
                price_for_stop_check = current_price
                
            if price_for_stop_check > white_line_5m.iloc[-1]:
                white_line_broken = True
                print(f"空单止损信号：检测到突破白线压力（价格{price_for_stop_check}高于白线{white_line_5m.iloc[-1]}）")
        
        if white_line_broken:
            # 计算当前盈亏
            profit_pct = calculate_profit(current_price, g.open_price, False)
            
            # 执行止损
            passorder(9, 1101, account, g.code, 14, 0, g.position_size, '', 1, '空单白线止损', ContextInfo)
            log_trade("空单白线止损", current_price, profit_pct)
            g.position = "none"
            g.position_size = 0
            sync_position_status(ContextInfo)
            g.trace_time_short = time.time()  # 白线止损平空时重置开空计时
            return True
    
    return False

def execute_take_profit(ContextInfo, current_price):
    """
    执行分批止盈策略（ATR倍数方式，分品种适配，分档最小点数保护，止盈价递增保护）
    """
    if not hasattr(g, 'open_atr') or g.open_atr is None:
        g.open_atr = calculate_atr(ContextInfo)
    code = g.code if hasattr(g, 'code') else ''
    take_profit_atr_1 = get_take_profit_atr_1(code)
    min_take_profit_points = get_min_take_profit_points(code)
    open_atr = g.open_atr
    # 获取tick_size
    tick_size = 1
    try:
        if hasattr(g, 'info') and g.info and hasattr(g.info, 'm_dMinPriceChange'):
            tick_size = float(g.info.m_dMinPriceChange)
    except Exception:
        tick_size = 1
    # 多单止盈逻辑
    if g.position == "long":
        profit_points = current_price - g.open_price
        tp1 = max(g.open_price + open_atr * take_profit_atr_1, g.open_price + min_take_profit_points)
        tp2 = max(g.open_price + open_atr * Config.TAKE_PROFIT_ATR_2, g.open_price + 2 * min_take_profit_points, tp1 + tick_size)
        tp3 = max(g.open_price + open_atr * Config.TAKE_PROFIT_ATR_3, g.open_price + 3 * min_take_profit_points, tp2 + tick_size)
        total_hands = Config.TRADE_HANDS
        if not g.profit_taken_1 and current_price >= tp1:
            first_batch = max(1, int(total_hands * Config.PROFIT_PCT_1))
            first_batch = min(first_batch, g.position_size)
            if first_batch > 0:
                passorder(7, 1101, account, g.code, 14, 0, first_batch, '', 1, '多单首批止盈', ContextInfo)
                g.profit_taken_1 = True
                g.position_size -= first_batch
                print(f"[止盈] 多单首批止盈价格: {current_price}")
                log_trade("多单首批止盈(30%)", current_price, profit_points)
                g.position_size = 0 if g.position_size < 1e-6 else g.position_size
                g.position = "none" if g.position_size == 0 else g.position
                sync_position_status(ContextInfo)
                return True
        elif g.profit_taken_1 and not g.profit_taken_2 and current_price >= tp2:
            second_batch = max(1, int(total_hands * Config.PROFIT_PCT_2))
            second_batch = min(second_batch, g.position_size)
            if second_batch > 0:
                passorder(7, 1101, account, g.code, 14, 0, second_batch, '', 1, '多单第二批止盈', ContextInfo)
                g.profit_taken_2 = True
                g.position_size -= second_batch
                print(f"[止盈] 多单第二批止盈价格: {current_price}")
                log_trade("多单第二批止盈(30%)", current_price, profit_points)
                g.position_size = 0 if g.position_size < 1e-6 else g.position_size
                g.position = "none" if g.position_size == 0 else g.position
                sync_position_status(ContextInfo)
                return True
        elif g.profit_taken_1 and g.profit_taken_2 and not g.profit_taken_3 and current_price >= tp3:
            third_batch = g.position_size
            if third_batch > 0:
                passorder(7, 1101, account, g.code, 14, 0, third_batch, '', 1, '多单第三批止盈', ContextInfo)
                g.profit_taken_3 = True
                g.position_size = 0
                g.position = "none"
                print(f"[止盈] 多单第三批止盈价格: {current_price}")
                log_trade("多单第三批止盈(40%)", current_price, profit_points)
                sync_position_status(ContextInfo)
                g.trace_time_long = time.time()  # 止盈平多时重置开多计时
                return True
    # 空单止盈逻辑
    elif g.position == "short":
        profit_points = g.open_price - current_price
        tp1 = min(g.open_price - open_atr * take_profit_atr_1, g.open_price - min_take_profit_points)
        tp2 = min(g.open_price - open_atr * Config.TAKE_PROFIT_ATR_2, g.open_price - 2 * min_take_profit_points, tp1 - tick_size)
        tp3 = min(g.open_price - open_atr * Config.TAKE_PROFIT_ATR_3, g.open_price - 3 * min_take_profit_points, tp2 - tick_size)
        total_hands = Config.TRADE_HANDS
        if not g.profit_taken_1_short and current_price <= tp1:
            first_batch = max(1, int(total_hands * Config.PROFIT_PCT_1))
            first_batch = min(first_batch, g.position_size)
            if first_batch > 0:
                passorder(9, 1101, account, g.code, 14, 0, first_batch, '', 1, '空单首批止盈', ContextInfo)
                g.profit_taken_1_short = True
                g.position_size -= first_batch
                print(f"[止盈] 空单首批止盈价格: {current_price}")
                log_trade("空单首批止盈(30%)", current_price, profit_points)
                g.position_size = 0 if g.position_size < 1e-6 else g.position_size
                g.position = "none" if g.position_size == 0 else g.position
                sync_position_status(ContextInfo)
                return True
        elif g.profit_taken_1_short and not g.profit_taken_2_short and current_price <= tp2:
            second_batch = max(1, int(total_hands * Config.PROFIT_PCT_2))
            second_batch = min(second_batch, g.position_size)
            if second_batch > 0:
                passorder(9, 1101, account, g.code, 14, 0, second_batch, '', 1, '空单第二批止盈', ContextInfo)
                g.profit_taken_2_short = True
                g.position_size -= second_batch
                print(f"[止盈] 空单第二批止盈价格: {current_price}")
                log_trade("空单第二批止盈(30%)", current_price, profit_points)
                g.position_size = 0 if g.position_size < 1e-6 else g.position_size
                g.position = "none" if g.position_size == 0 else g.position
                sync_position_status(ContextInfo)
                return True
        elif g.profit_taken_1_short and g.profit_taken_2_short and not g.profit_taken_3_short and current_price <= tp3:
            third_batch = g.position_size
            if third_batch > 0:
                passorder(9, 1101, account, g.code, 14, 0, third_batch, '', 1, '空单第三批止盈', ContextInfo)
                g.profit_taken_3_short = True
                g.position_size = 0
                g.position = "none"
                print(f"[止盈] 空单第三批止盈价格: {current_price}")
                log_trade("空单第三批止盈(40%)", current_price, profit_points)
                sync_position_status(ContextInfo)
                g.trace_time_short = time.time()  # 止盈平空时重置开空计时
                return True
    return False

def check_volume_increase(volume_data, threshold_base=1.2):
    """
    检查成交量是否增加
    
    参数:
    volume_data: 成交量数据
    threshold_base: 基础阈值倍数
    
    返回:
    True: 成交量增加
    False: 成交量未增加
    """
    try:
        # 使用品种特定的阈值
        threshold = g.volume_threshold if hasattr(g, 'volume_threshold') else threshold_base
        
        # 确保数据足够
        if len(volume_data) < 2:
            return False
            
        # 计算当前成交量与前一根K线成交量的比值
        volume_ratio = volume_data.iloc[-1] / volume_data.iloc[-2]
        
        # 返回是否超过阈值
        return volume_ratio > threshold
    except Exception as e:
        print(f"检查成交量增加出错: {e}")
        return False

def check_trend(price_data, ma_value, lookback=5):
    """
    检查价格趋势 - 优化版
    
    参数:
    price_data: 价格数据
    ma_value: 均线值
    lookback: 回溯周期
    
    返回:
    1: 上升趋势
    0: 无明显趋势
    -1: 下降趋势
    """
    # 计算短期趋势
    short_trend = price_data['close'].iloc[-1] - price_data['close'].iloc[-3]
    
    # 计算价格相对均线位置
    price_vs_ma = price_data['close'].iloc[-1] - ma_value
    
    # 判断趋势
    if short_trend > 0 and price_vs_ma > 0:
        return 1  # 上升趋势
    elif short_trend < 0 and price_vs_ma < 0:
        return -1  # 下降趋势
    
    # 如果短期趋势和均线位置不一致，以短期趋势为准
    if abs(short_trend) > abs(price_vs_ma) * 0.01:  # 短期趋势明显
        return 1 if short_trend > 0 else -1
        
    return 0  # 无明显趋势

def should_reverse_trade(ContextInfo, last_position_type, current_price):
    """
    评估是否应该进行反向交易
    
    参数:
    ContextInfo: 上下文信息
    last_position_type: 上一个持仓类型 ("long" 或 "short")
    current_price: 当前价格
    
    返回:
    (should_reverse, reverse_type, confidence_score)
    """
    # 检查距离上次平仓的时间
    time_since_close = time.time() - g.last_close_time
    min_wait_seconds = 5 * 60  # 最少等待5分钟
    max_wait_seconds = 30 * 60  # 最多等待30分钟
    
    if time_since_close < min_wait_seconds:
        print(f"反向交易评估: 距离上次平仓时间不足5分钟，暂不考虑反向")
        return (False, None, 0)
    
    if time_since_close > max_wait_seconds:
        print(f"反向交易评估: 距离上次平仓时间超过30分钟，不再考虑反向")
        return (False, None, 0)
    
    # 获取价格数据
    price_5m = ContextInfo.get_market_data(['close','high','low','open','volume'], 
                                       [g.code], 
                                       period='5m',
                                       count=60)
    
    # 计算技术指标
    MA_Line = calculate_ma(price_5m['close'], price_5m['volume'], 55)
    white_line_5m, red_line_5m = calculate_red_white_lines_exact(price_5m)
    
    # 计算RSI
    delta = price_5m['close'].diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    avg_gain = gain.rolling(window=9).mean()
    avg_loss = loss.rolling(window=9).mean()
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    current_rsi = rsi.iloc[-1]
    
    # 初始化信心分数
    confidence_score = 0
    
    # 根据上一个持仓类型决定反向交易方向
    if last_position_type == "long":
        # 考虑做空
        reverse_type = "short"
        
        # 核心条件 - 价格在MA线下方
        if current_price < MA_Line.iloc[-1]:
            confidence_score += 30
            print(f"反向交易评估(空): 价格在MA线下方")
        
        # 白线压力确认
        if white_line_5m.iloc[-1] is not None and not pd.isna(white_line_5m.iloc[-1]) and current_price < white_line_5m.iloc[-1]:
            confidence_score += 30
            print("反向交易评估(空): 白线压力确认")
        
        # RSI超买检查
        if current_rsi > 70:
            confidence_score += 20
            print(f"反向交易评估(空): RSI超买 ({current_rsi:.1f})")
        
        # 跌破红线
        first_break_red = False
        if len(red_line_5m) > 1 and red_line_5m.iloc[-2] is not None and not pd.isna(red_line_5m.iloc[-2]):  # 前一根K线有红线
            # 情况1：标准检测 - 前一根K线有红线，当前K线红线消失
            if (red_line_5m.iloc[-1] is None or pd.isna(red_line_5m.iloc[-1])) and price_5m['close'].iloc[-1] < red_line_5m.iloc[-2]:
                first_break_red = True
                print(f"检测到标准跌破红线信号: 价格{price_5m['close'].iloc[-1]}跌破红线{red_line_5m.iloc[-2]}")
                confidence_score += 20
            
            # 情况2：强力跌破 - 即使红线没消失，但价格大幅低于红线
            elif red_line_5m.iloc[-1] is not None and not pd.isna(red_line_5m.iloc[-1]):
                # 价格低于红线1%以上视为强力跌破
                price_below_red = (red_line_5m.iloc[-1] - price_5m['close'].iloc[-1]) / red_line_5m.iloc[-1] > 0.01
                if price_below_red:
                    first_break_red = True
                    print(f"检测到强力跌破红线信号: 价格{price_5m['close'].iloc[-1]}大幅低于红线{red_line_5m.iloc[-1]}")
                    confidence_score += 20
    
    elif last_position_type == "short":
        # 考虑做多
        reverse_type = "long"
        
        # 核心条件 - 价格在MA线上方
        if current_price > MA_Line.iloc[-1]:
            confidence_score += 30
            print(f"反向交易评估(多): 价格在MA线上方")
        
        # 红线支撑确认
        if red_line_5m.iloc[-1] is not None and not pd.isna(red_line_5m.iloc[-1]) and current_price > red_line_5m.iloc[-1]:
            confidence_score += 30
            print("反向交易评估(多): 红线支撑确认")
        
        # RSI超卖检查
        if current_rsi < 30:
            confidence_score += 20
            print(f"反向交易评估(多): RSI超卖 ({current_rsi:.1f})")
        
        # 突破白线
        first_break_white = False
        if len(white_line_5m) > 1 and white_line_5m.iloc[-2] is not None and not pd.isna(white_line_5m.iloc[-2]):
            if (white_line_5m.iloc[-1] is None or pd.isna(white_line_5m.iloc[-1])) and current_price > white_line_5m.iloc[-2]:
                first_break_white = True
                confidence_score += 20
                print("反向交易评估(多): 突破白线")
    
    else:
        # 无法确定上一个持仓类型
        return (False, None, 0)
    
    # 增加趋势确认条件
    price_30m = ContextInfo.get_market_data(['close','high','low','open','volume'], 
                                       [g.code], 
                                       period='30m',
                                       count=60)
    
    # 计算30分钟趋势
    trend_30m = check_trend(price_30m, MA_Line.iloc[-1])
    
    # 趋势一致性检查
    if reverse_type == "long" and trend_30m == 1:
        confidence_score += 20
        print("反向交易评估(多): 30分钟趋势向上")
    elif reverse_type == "short" and trend_30m == -1:
        confidence_score += 20
        print("反向交易评估(空): 30分钟趋势向下")
    
    # 信心分数阈值
    should_reverse = confidence_score >= 60
    
    # 记录最终决策
    print(f"反向交易评估: 最终信心分数 {confidence_score}/100, {'执行' if should_reverse else '不执行'}反向{reverse_type}交易")
    
    return (should_reverse, reverse_type, confidence_score)

def get_future_positions(ContextInfo, accountid):
    """
    获取期货持仓信息
    
    参数:
    ContextInfo: 上下文信息
    accountid: 账户ID
    
    返回:
    持仓字典，格式为 {(合约代码, 方向): 持仓量}
    """
    positions = get_trade_detail_data(accountid, 'FUTURE', 'POSITION')
    hold_dict = {}
    for p in positions:
        market = p.m_strExchangeID
        market = {"SHFE":'SF',"CZCE":'ZF',"DCE":'DF',"CFFEX":'IF'}.get(p.m_strExchangeID, p.m_strExchangeID)

        code = p.m_strInstrumentID +'.'+market
        direction = p.m_nDirection
        volume = p.m_nVolume
        key = (code, direction)
        hold_dict[key] = hold_dict.get(key, 0)+volume
    return hold_dict
    

def order_callback(ContextInfo, orderInfo):
    """订单状态回调函数"""
    print(orderInfo.m_strRemark, orderInfo.m_nOrderStatus, orderInfo.m_nOffsetFlag, orderInfo.m_dTradedPrice)
    
    # 处理限价单超时逻辑
    if orderInfo.m_strRemark in ['期货策略限价开多', '期货策略限价开空'] and orderInfo.m_nOrderStatus == 48:  # 48是未成交状态
        # 检查订单是否超过设定的等待时间
        current_time = time.time()
        order_time = orderInfo.m_createtime if hasattr(orderInfo, 'm_createtime') else current_time - 60  # 默认一分钟前
        
        # 如果超过等待时间（例如3分钟），则撤单并改用市价单
        if current_time - order_time > Config.PRICE_ORDER_TIMEOUT * 60:
            print(f"限价单等待超时，改用市价单: {orderInfo.m_strRemark}")
            
            # 撤销原订单
            ContextInfo.cancel_order(orderInfo.m_strOrderSysID)
            
            # 使用市价单重新下单
            if '开多' in orderInfo.m_strRemark:
                passorder(0, 1101, account, g.code, 14, 0, orderInfo.m_nVolume, '', 1, '期货策略开多', ContextInfo)
            elif '开空' in orderInfo.m_strRemark:
                passorder(3, 1101, account, g.code, 14, 0, orderInfo.m_nVolume, '', 1, '期货策略开空', ContextInfo)
    
    # 原有代码保持不变
    # ... 其余代码 ...

def orderError_callback(ContextInfo, passOrderInfo, msg):
    """
    订单错误回调函数 - 处理订单错误
    
    参数:
    ContextInfo: 上下文信息
    passOrderInfo: 订单信息
    msg: 错误信息
    """
    if '期货策略' not in passOrderInfo.strategyName:
        return
        
    g.hold = 0
    print("orderError_callback set 0", msg)
    
    if '期货策略开空' in passOrderInfo.strategyName:
        g.buy_short += 1
        print(f"{g.code} 开空次数+1 {g.buy_short}")
    
    if '期货策略开多' in passOrderInfo.strategyName:
        g.buy_long += 1
        print(f"{g.code} 开多次数+1 {g.buy_long}")


def deal_callback(ContextInfo, dealInfo):
    """
    成交回报回调函数 - 处理成交信息
    
    参数:
    ContextInfo: 上下文信息
    dealInfo: 成交信息
    """
    # 处理订单系统ID
    if g.sysid:
        if dealInfo.m_strOrderSysID in g.sysid:  # 如果是order_callback先收到
            g.open_price, g.hold_price = round(dealInfo.m_dPrice, 1), round(dealInfo.m_dPrice, 1)
        else:
            g.sysid[dealInfo.m_strOrderSysID] = [round(dealInfo.m_dPrice, 1), round(dealInfo.m_dPrice, 1)]
    else:  # 如果是deal_callback先收到
        g.sysid[dealInfo.m_strOrderSysID] = [round(dealInfo.m_dPrice, 1), round(dealInfo.m_dPrice, 1)]

    print(f"deal callback m_nOffsetFlag:[{dealInfo.m_nOffsetFlag}] m_strRemark:[{dealInfo.m_strRemark}], [{dealInfo.m_strInstrumentID}] [{dealInfo.m_strExchangeID}] [{dealInfo.m_dPrice}]")
    
    if dealInfo.m_strRemark not in ['期货策略开多','期货策略开空']:
        return
        
    market = {"SHFE":'SF',"CZCE":'ZF',"DCE":'DF',"CFFEX":'IF'}.get(dealInfo.m_strExchangeID, dealInfo.m_strExchangeID)
    k = dealInfo.m_strInstrumentID+'.'+market
    
    if dealInfo.m_nOffsetFlag == 48 and g.code.find(dealInfo.m_strInstrumentID)>=0:
        print("deal callback", dealInfo.m_dPrice)
        g.open_price = round(dealInfo.m_dPrice, 1)
        g.hold_price = round(dealInfo.m_dPrice, 1)
        
        # 更新持仓状态
        if dealInfo.m_strRemark == '期货策略开多':
            g.position = "long"
            g.position_size = Config.TRADE_HANDS
            # 重置止盈标记
            g.profit_taken_1 = False
            g.profit_taken_2 = False
            g.profit_taken_3 = False
            
        elif dealInfo.m_strRemark == '期货策略开空':
            g.position = "short"
            g.position_size = Config.TRADE_HANDS
            # 重置止盈标记
            g.profit_taken_1_short = False
            g.profit_taken_2_short = False
            g.profit_taken_3_short = False

# ====== 技术指标计算函数 ======
def REF(S, N=1):
    """
    对序列整体下移动N,返回序列(shift后会产生NAN)
    
    参数:
    S: 输入序列
    N: 移动周期数
    
    返回:
    移动后的序列
    """
    return pd.Series(S).shift(N).values

def SMA(S, N, M=1):
    """
    中国式的SMA,至少需要120周期才精确 (雪球180周期)
    
    参数:
    S: 输入序列
    N: 周期数
    M: 权重因子
    
    返回:
    SMA序列
    """
    alpha = 1/(1+N-M/M)  # 等价于 alpha=M/N
    return pd.Series(S).ewm(alpha=M/N, adjust=False).mean().values

def SUM(S, N):
    """
    对序列求N天累计和，返回序列
    N=0对序列所有依次求和
    
    参数:
    S: 输入序列
    N: 周期数
    
    返回:
    累计和序列
    """
    return pd.Series(S).rolling(N).sum().values if N>0 else pd.Series(S).cumsum().values

def HHV(S, N):
    """
    求N周期内的最高值
    例如: HHV(C, 5) 最近5天收盘最高价
    
    参数:
    S: 输入序列
    N: 周期数
    
    返回:
    最高值序列
    """
    return pd.Series(S).rolling(N).max().values

def LLV(S, N):
    """
    求N周期内的最低值
    例如: LLV(C, 5) 最近5天收盘最低价
    
    参数:
    S: 输入序列
    N: 周期数
    
    返回:
    最低值序列
    """
    return pd.Series(S).rolling(N).min().values

def HHVBARS(S, N):
    """
    求N周期内S最高值到当前周期数, 返回序列
    
    参数:
    S: 输入序列
    N: 周期数
    
    返回:
    最高值位置序列
    """
    return pd.Series(S).rolling(N).apply(lambda x: np.argmax(x[::-1]), raw=True).values

def LLVBARS(S, N):
    """
    求N周期内S最低值到当前周期数, 返回序列
    
    参数:
    S: 输入序列
    N: 周期数
    
    返回:
    最低值位置序列
    """
    return pd.Series(S).rolling(N).apply(lambda x: np.argmin(x[::-1]), raw=True).values

def MA(S, N):
    """
    求序列的N日简单移动平均值，返回序列
    
    参数:
    S: 输入序列
    N: 周期数
    
    返回:
    移动平均序列
    """
    return pd.Series(S).rolling(N).mean().values

def EMA(source, N:int, result_type='np'):
    """
    求指数平滑移动平均.
    用法:
    EMA(X,N),求X的N日指数平滑移动平均.算法：若Y=EMA(X,N)
    则Y=[2*X+(N-1)*Y']/(N+1),其中Y'表示上一周期Y值.
    例如：EMA(CLOSE,30)表示求30日指数平滑均价
    
    参数:
    source: 输入序列
    N: 周期数
    result_type: 返回类型，'np'表示numpy数组
    
    返回:
    EMA序列
    """
    M = 2
    if N<M:
        raise ValueError(f"N:{N}必须大于{M}")
    result = []
    temp = None
    d = N-1
    M = 2
    for pos, x in enumerate(source):
        if pos == 0:
            result.append(x)
            temp = x
        else:
            temp = (M*x+d*temp)/(N+1)
            result.append(temp)
    if result_type == 'np':
        return np.array(result)
    else:
        return result

def PyLLV(S, N):
    """
    Python实现的LLV函数，适用于pandas Series
    
    参数:
    S: 输入Series
    N: 周期数
    
    返回:
    最低值Series
    """
    index = S.index
    result_list = []
    slist = list(S)

    for i in range(len(S.index)):
        l = slist[max(0, i+1-N):i+1]
        result_list.append(min(l))
    return pd.Series(result_list, index=index)

def PyHHV(S, N):
    """
    Python实现的HHV函数，适用于pandas Series
    
    参数:
    S: 输入Series
    N: 周期数
    
    返回:
    最高值Series
    """
    index = S.index
    result_list = []
    slist = list(S)

    for i in range(len(S.index)):
        l = slist[max(0, i+1-N):i+1]
        result_list.append(max(l))
    return pd.Series(result_list, index=index)

def after_init(ContextInfo):
    """
    在初始化后执行，用于获取交易日历信息
    
    参数:
    ContextInfo: 上下文信息
    
    返回:
    交易日期列表
    """
    return ContextInfo.get_trading_dates(g.market,'','********',count=2, period='1d')

def sync_position_status(ContextInfo):
    """同步持仓状态，确保g.position与实际持仓一致"""
    positions = get_future_positions(ContextInfo, account)
    
    # 检查是否有多头持仓
    has_long_position = False
    long_volume = 0
    for (code, direction), volume in positions.items():
        if code == g.code and direction == 48 and volume > 0:
            has_long_position = True
            long_volume = volume
            break
    
    # 检查是否有空头持仓
    has_short_position = False
    short_volume = 0
    for (code, direction), volume in positions.items():
        if code == g.code and direction == 49 and volume > 0:
            has_short_position = True
            short_volume = volume
            break
    
    # 更新系统状态
    if has_long_position:
        if g.position != "long":
            log_trade("同步状态：检测到多头持仓但系统状态不一致，已更新", 0)
        g.position = "long"
        g.position_size = long_volume
    elif has_short_position:
        if g.position != "short":
            log_trade("同步状态：检测到空头持仓但系统状态不一致，已更新", 0)
        g.position = "short"
        g.position_size = short_volume
    else:
        if g.position != "none":
            log_trade("同步状态：没有检测到持仓但系统状态不一致，已更新", 0)
        g.position = "none"
        g.position_size = 0

def determine_position_level(ContextInfo, code, current_price):
    """
    根据当前价格确定仓位级别
    
    参数:
    ContextInfo: 上下文信息
    code: 合约代码
    current_price: 当前价格
    
    返回:
    仓位级别
    """
    # 实现仓位级别判断逻辑
    # 这里可以根据实际需求实现不同的判断逻辑
    # 例如，可以根据价格波动幅度、成交量等因素来确定仓位级别
    return 1  # 临时返回值，需要根据实际逻辑实现



def calculate_atr(ContextInfo, period=None):  # 5分钟ATR
    if period is None:
        period = Config.ATR_WINDOW_5M
    price_data = ContextInfo.get_market_data(['high', 'low', 'close'], 
                                         [g.code], 
                                         period='5m',   # 5分钟K线
                                         count=period+10)
    # 计算真实波动幅度
    tr1 = abs(price_data['high'] - price_data['low'])
    tr2 = abs(price_data['high'] - price_data['close'].shift(1))
    tr3 = abs(price_data['low'] - price_data['close'].shift(1))
    # 取三者最大值
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    # 计算ATR
    atr = tr.rolling(window=period).mean().iloc[-1]
    return atr

def check_pullback_volume(volume):
    """
    专门针对回调买入的成交量检查
    
    参数:
    volume: 成交量序列
    
    返回:
    True: 成交量符合回调买入要求
    False: 成交量不符合回调买入要求
    """
    # 回调阶段（前几根K线）成交量应该减少
    pullback_volume_decrease = volume.iloc[-3:-1].mean() < volume.iloc[-6:-3].mean()
    
    # 当前K线（反弹确认）成交量应该增加
    current_volume_increase = volume.iloc[-1] > volume.iloc[-2]
    
    # 综合判断
    return pullback_volume_decrease and current_volume_increase

def check_downtrend_volume(volume, close):
    """
    专门针对下跌行情的成交量检查
    
    参数:
    volume: 成交量序列
    close: 收盘价序列
    
    返回:
    True: 成交量符合下跌行情特征
    False: 成交量不符合下跌行情特征
    """
    # 计算价格变化
    price_change = close.pct_change()
    
    # 下跌确认
    is_declining = close.iloc[-1] < close.iloc[-3]
    
    # 情况1: 放量下跌 - 传统意义上的看空信号
    volume_increase = volume.iloc[-1] > volume.iloc[-2] * 1.2
    
    # 情况2: 缩量下跌 - 买盘撤离导致的下跌
    volume_decrease = volume.iloc[-1] < volume.iloc[-5:].mean()
    price_decline = price_change.iloc[-1] < -0.002  # 价格下跌超过0.2%
    
    # 情况3: 连续下跌 - 持续的下跌趋势
    continuous_decline = all(price_change.iloc[-3:] < 0)
    
    # 综合判断
    return is_declining and (volume_increase or (volume_decrease and price_decline) or continuous_decline)

def detect_line_transition(prev_white, curr_white, prev_red, curr_red):
    """
    检测红白线转换
    
    参数:
    prev_white: 前一根K线的白线
    curr_white: 当前K线的白线
    prev_red: 前一根K线的红线
    curr_red: 当前K线的红线
    
    返回:
    1: 白线转红线
    -1: 红线转白线
    0: 无转换
    """
    try:
        # 减少调试信息输出
        # print("\n=== 红白线转换检测 ===")
        # print(f"前一根K线白线: {prev_white}")
        # print(f"当前K线白线: {curr_white}")
        # print(f"前一根K线红线: {prev_red}")
        # print(f"当前K线红线: {curr_red}")
        # 
        # print(f"转换检测详情: 前白={prev_white}, 当白={curr_white}, 前红={prev_red}, 当红={curr_red}")
        
        # 白线转红线
        if (prev_white is not None and not pd.isna(prev_white) and 
            (curr_red is not None and not pd.isna(curr_red)) and 
            (curr_white is None or pd.isna(curr_white))):
            print(f"检测到标准白线转红线: {prev_white} -> {curr_red}")
            return 1
            
        # 红线转白线
        if (prev_red is not None and not pd.isna(prev_red) and 
            (curr_white is not None and not pd.isna(curr_white)) and 
            (curr_red is None or pd.isna(curr_red))):
            print(f"检测到标准红线转白线: {prev_red} -> {curr_white}")
            return -1
            
        return 0
    except Exception as e:
        print(f"检测红白线转换出错: {e}")
        return 0

# 在全局作用域中定义函数
def check_historical_transition(white_line, red_line):
    """
    检查历史数据中是否有白线转红线
    
    参数:
    white_line: 白线序列
    red_line: 红线序列
    
    返回:
    (has_transition, bars_ago): 是否有转换，以及发生在多少根K线之前
    """
    try:
        # 确保数据足够
        if white_line is None or red_line is None or len(white_line) < 5 or len(red_line) < 5:
            return False, 0
            
        # 从最近的数据开始向前查找
        for i in range(len(white_line)-2, 0, -1):
            # 检查是否有白线转红线
            if (not pd.isna(white_line.iloc[i]) and white_line.iloc[i] is not None and 
                pd.isna(white_line.iloc[i+1]) and 
                not pd.isna(red_line.iloc[i+1]) and red_line.iloc[i+1] is not None):
                
                # 计算发生在多少根K线之前
                bars_ago = len(white_line) - 1 - i
                print(f"检测到白线转红线发生在{bars_ago}根K线之前")
                return True, bars_ago
                
        return False, 0
    except Exception as e:
        print(f"检查历史转换出错: {e}")
        return False, 0

# 在handlebar函数中，在使用can_open_position之前添加定义

# 其他可能的限制条件
# ...

# 修改红白线检查函数
def check_break_red_line(price_data, red_line_prev, red_line_curr, current_price):
    """
    检查是否跌破红线
    
    参数:
    price_data: 价格数据
    red_line_prev: 前一根K线的红线
    red_line_curr: 当前K线的红线
    current_price: 当前价格
    
    返回:
    是否跌破红线
    """
    try:
        # 减少调试信息输出
        # print("=== 跌破红线详细检查 ===")
        # print(f"前一根K线红线: {red_line_prev}")
        # print(f"当前K线红线: {red_line_curr}")
        # 
        # if price_data is None or price_data.empty:
        #     print("价格数据为空")
        #     return False
        #     
        # current_price = price_data['close'].iloc[-1]
        # print(f"当前收盘价: {current_price}")
        # 
        # # 添加空值检查 - 红线
        # if red_line_prev is not None and not pd.isna(red_line_prev) and current_price is not None:
        #     price_prev_pct = (current_price - red_line_prev) / red_line_prev * 100
        #     print(f"价格与前一根红线比较: {price_prev_pct:.2f}%")
        # else:
        #     print("前一根红线不存在或为空，无法计算百分比")
        #     price_prev_pct = None
        #     
        # if red_line_curr is not None and not pd.isna(red_line_curr) and current_price is not None:
        #     price_curr_pct = (current_price - red_line_curr) / red_line_curr * 100
        #     print(f"价格与当前红线比较: {price_curr_pct:.2f}%")
        #     
        #     # 检查价格与红线的距离（用于做多）
        #     red_line_distance = (current_price - red_line_curr) / red_line_curr * 100
        #     red_line_distance_ok = red_line_distance <= Config.RED_LINE_DISTANCE_THRESHOLD * 100
        #     print(f"价格与红线距离: {red_line_distance:.2f}% {'(符合做多条件)' if red_line_distance_ok else '(超出做多阈值)'}")
        # else:
        #     print("当前红线不存在或为空，无法计算百分比")
        #     price_curr_pct = None
            
        # 获取5分钟白线值
        try:
            # 计算白线值
            white_line_data, _ = calculate_red_white_lines_exact(price_data)
            white_line_value = white_line_data.iloc[-1] if not white_line_data.empty else None
            
            # 如果白线存在，计算价格与白线的距离
            if white_line_value is not None and not pd.isna(white_line_value):
                white_line_distance = (white_line_value - current_price) / white_line_value * 100
                white_line_distance_ok = white_line_distance <= Config.WHITE_LINE_DISTANCE_THRESHOLD * 100
                print(f"价格与白线距离: {white_line_distance:.2f}% {'(符合做空条件)' if white_line_distance_ok else '(超出做空阈值)'}")
        except Exception as e:
            print(f"获取白线或计算白线距离时出错: {e}")
        
        # 判断是否跌破红线
        if red_line_prev is not None and not pd.isna(red_line_prev) and current_price < red_line_prev:
            return True
        if red_line_curr is not None and not pd.isna(red_line_curr) and current_price < red_line_curr:
            return True
            
        return False
    except Exception as e:
        print(f"检查红白线出错: {e}")
        return False

# 修改打印红白线详情函数
def print_line_details(price_data, white_line, red_line, current_price):
    """
    打印红白线详细信息
    
    参数:
    price_data: 价格数据
    white_line: 白线值
    red_line: 红线值
    current_price: 当前价格
    """
    try:
        print("\n=== 红白线详细信息 ===")
        
        if price_data is None or price_data.empty:
            print("价格数据为空")
            return
            
        print(f"当前价格: {current_price}")
        print(f"白线价格: {white_line}")
        print(f"红线价格: {red_line}")
        
        # 检查价格与白线的距离（用于做空）
        if white_line is not None and not pd.isna(white_line) and current_price is not None:
            white_line_distance = (white_line - current_price) / white_line * 100
            white_line_distance_ok = white_line_distance <= Config.WHITE_LINE_DISTANCE_THRESHOLD * 100
            print(f"价格与白线距离: {white_line_distance:.2f}% {'(符合做空条件)' if white_line_distance_ok else '(超出做空阈值)'}")
            
            # 检查是否突破白线
            if current_price > white_line:
                print(f"突破白线: 价格{current_price} > 白线{white_line}")
            else:
                print("无白线突破")
        else:
            print("无白线或白线为空")
            
        # 检查价格与红线的距离（用于做多）
        if red_line is not None and not pd.isna(red_line) and current_price is not None:
            red_line_distance = (current_price - red_line) / red_line * 100
            red_line_distance_ok = red_line_distance <= Config.RED_LINE_DISTANCE_THRESHOLD * 100
            print(f"价格与红线距离: {red_line_distance:.2f}% {'(符合做多条件)' if red_line_distance_ok else '(超出做多阈值)'}")
            
            # 检查是否跌破红线
            if current_price < red_line:
                print(f"跌破红线: 价格{current_price} < 红线{red_line}")
            else:
                print("无红线跌破")
        else:
            print("无红线或红线为空")
            
        # 打印最高价和最低价
        if 'high' in price_data.columns and 'low' in price_data.columns:
            print(f"最高价: {price_data['high'].iloc[-1]}")
            print(f"最低价: {price_data['low'].iloc[-1]}")
            
    except Exception as e:
        print(f"打印红白线详情出错: {e}")

# 添加新的红白线计算函数
def calculate_red_white_lines(price_data):
    """
    根据原始公式计算红白线指标
    
    参数:
    price_data: 包含open, high, low, close的DataFrame
    
    返回:
    white_line: 白线序列
    red_line: 红线序列
    """
    try:
        # 确保数据足够
        if len(price_data) < 10:
            return pd.Series([None] * len(price_data)), pd.Series([None] * len(price_data))
        
        # 计算高低点
        high = price_data['high']
        low = price_data['low']
        open_price = price_data['open']
        close = price_data['close']
        
        # 计算2根K线的最高价和最低价
        hx = high.rolling(window=2).max()
        lx = low.rolling(window=2).min()
        
        # 初始化结果序列
        white_line = pd.Series([None] * len(price_data), index=price_data.index)
        red_line = pd.Series([None] * len(price_data), index=price_data.index)
        k2 = pd.Series([0] * len(price_data), index=price_data.index)
        
        # 计算H1和L1
        for i in range(5, len(price_data)):
            # 白线条件
            if (lx[i] > lx[i-1] and lx[i] > lx[i-3] and lx[i] > lx[i-5] and 
                hx[i] > hx[i-1] and hx[i] > hx[i-2] and hx[i] > hx[i-4] and 
                open_price[i] < close[i] and 
                (close[i] - open_price.iloc[:i+1].min()) > 0):
                
                # 设置白线值为前4根K线的最低价
                white_line[i] = lx[i-4]
                k2[i] = 1
            
            # 红线条件
            elif (hx[i] < hx[i-1] and hx[i] < hx[i-2] and hx[i] < hx[i-4] and 
                  lx[i] < lx[i-1] and lx[i] < lx[i-3] and lx[i] < lx[i-5] and 
                  open_price[i] > close[i] and 
                  (open_price.iloc[:i+1].max() - close[i]) > 0):
                
                # 设置红线值为前4根K线的最高价
                red_line[i] = hx[i-4]
                k2[i] = -3
        
        # 填充空值 - 使用前一个有效值
        for i in range(1, len(price_data)):
            if k2[i] == 0:
                k2[i] = k2[i-1]
            
            if k2[i] == 1 and pd.isna(white_line[i]):
                # 找到前一个白线值
                for j in range(i-1, -1, -1):
                    if not pd.isna(white_line[j]):
                        white_line[i] = white_line[j]
                        break
            
            elif k2[i] == -3 and pd.isna(red_line[i]):
                # 找到前一个红线值
                for j in range(i-1, -1, -1):
                    if not pd.isna(red_line[j]):
                        red_line[i] = red_line[j]
                        break
        
        # 减少调试信息输出
        # print(f"红白线计算完成: 数据长度={len(price_data)}, 白线非空值={white_line.notna().sum()}, 红线非空值={red_line.notna().sum()}")
        return white_line, red_line
    
    except Exception as e:
        print(f"计算红白线出错: {e}")
        # 返回空序列
        return pd.Series([None] * len(price_data), index=price_data.index), pd.Series([None] * len(price_data), index=price_data.index)

def calculate_red_white_lines_exact(price_data):
    """
    严格按照文华指标源代码计算红白线
    
    参数:
    price_data: 包含open, high, low, close的DataFrame
    
    返回:
    white_line: 白线序列
    red_line: 红线序列
    """
    try:
        # 确保数据足够
        if len(price_data) < 10:
            return pd.Series([None] * len(price_data)), pd.Series([None] * len(price_data))
        
        # 计算HX和LX
        high = price_data['high']
        low = price_data['low']
        open_price = price_data['open']
        close = price_data['close']
        
        # HX:=HHV(HIGH,2);
        hx = high.rolling(window=2).max()
        
        # LX:=LLV(LOW,2);
        lx = low.rolling(window=2).min()
        
        # 初始化H1, L1, K1, K2, G
        h1 = pd.Series([0] * len(price_data), index=price_data.index)
        l1 = pd.Series([0] * len(price_data), index=price_data.index)
        k1 = pd.Series([0] * len(price_data), index=price_data.index)
        k2 = pd.Series([0] * len(price_data), index=price_data.index)
        g = pd.Series([None] * len(price_data), index=price_data.index)
        
        # 计算H1和L1
        for i in range(5, len(price_data)):
            # H1:=IFELSE(HX<REF(HX,1)&&HX<REF(HX,2)&&HX<REF(HX,4)&&LX<REF(LX,1)&&LX<REF(LX,3)&&LX<REF(LX,5)&&OPEN>CLOSE&&(HHV(OPEN,0)-CLOSE)>0,REF(HX,4),0);
            if (hx[i] < hx[i-1] and hx[i] < hx[i-2] and hx[i] < hx[i-4] and 
                lx[i] < lx[i-1] and lx[i] < lx[i-3] and lx[i] < lx[i-5] and 
                open_price[i] > close[i] and 
                (open_price.iloc[:i+1].max() - close[i]) > 0):
                h1[i] = hx[i-4]
            else:
                h1[i] = 0
            
            # L1:=IFELSE(LX>REF(LX,1)&&LX>REF(LX,3)&&LX>REF(LX,5)&&HX>REF(HX,1)&&HX>REF(HX,2)&&HX>REF(HX,4)&&OPEN<CLOSE&&(CLOSE-LLV(OPEN,0))>0,REF(LX,4),0);
            if (lx[i] > lx[i-1] and lx[i] > lx[i-3] and lx[i] > lx[i-5] and 
                hx[i] > hx[i-1] and hx[i] > hx[i-2] and hx[i] > hx[i-4] and 
                open_price[i] < close[i] and 
                (close[i] - open_price.iloc[:i+1].min()) > 0):
                l1[i] = lx[i-4]
            else:
                l1[i] = 0
        
        # H2:=VALUEWHEN(H1>0,H1);
        h2 = pd.Series([None] * len(price_data), index=price_data.index)
        last_valid_h1 = None
        for i in range(len(price_data)):
            if h1[i] > 0:
                last_valid_h1 = h1[i]
            h2[i] = last_valid_h1
        
        # L2:=VALUEWHEN(L1>0,L1);
        l2 = pd.Series([None] * len(price_data), index=price_data.index)
        last_valid_l1 = None
        for i in range(len(price_data)):
            if l1[i] > 0:
                last_valid_l1 = l1[i]
            l2[i] = last_valid_l1
        
        # K1:=IFELSE(CLOSE>H2,-3,IFELSE(CLOSE<L2,1,0));
        for i in range(len(price_data)):
            if h2[i] is not None and close[i] > h2[i]:
                k1[i] = -3
            elif l2[i] is not None and close[i] < l2[i]:
                k1[i] = 1
            else:
                k1[i] = 0
        
        # K2:=VALUEWHEN(K1<>0,K1);
        last_valid_k1 = 0
        for i in range(len(price_data)):
            if k1[i] != 0:
                last_valid_k1 = k1[i]
            k2[i] = last_valid_k1
        
        # G:=IFELSE(K2=1,H2,L2);
        for i in range(len(price_data)):
            if k2[i] == 1:
                g[i] = h2[i]
            else:
                g[i] = l2[i]
        
        # 转换为红白线
        white_line = pd.Series([None] * len(price_data), index=price_data.index)
        red_line = pd.Series([None] * len(price_data), index=price_data.index)
        
        for i in range(len(price_data)):
            if k2[i] == 1:  # 白线
                white_line[i] = g[i]
                red_line[i] = None
            elif k2[i] == -3:  # 红线
                red_line[i] = g[i]
                white_line[i] = None
        
        # 减少调试信息输出
        # print(f"文华指标红白线计算完成: 数据长度={len(price_data)}, 白线非空值={white_line.notna().sum()}, 红线非空值={red_line.notna().sum()}")
        return white_line, red_line
    
    except Exception as e:
        print(f"计算文华指标红白线出错: {e}")
        # 返回空序列
        return pd.Series([None] * len(price_data), index=price_data.index), pd.Series([None] * len(price_data), index=price_data.index)

def should_trade_with_transition(transition_signal, current_price, MA_Line, trend, volume_data, 
                               white_line=None, red_line=None, time_filter=True, ma55_value=None,
                               prev_white=None, prev_red=None):  # 添加前一根K线的线值
    
    # 解析转换信号
    transition_long = transition_signal == 1  # 白线转红线，做多信号
    transition_short = transition_signal == -1  # 红线转白线，做空信号
    
    # 价格与MA的关系
    price_above_ma = current_price > MA_Line
    
    # 趋势方向
    trend_up = trend == 1
    trend_down = trend == -1
    
    # 成交量确认
    volume_confirmed = check_volume_increase(volume_data, Config.VOLUME_THRESHOLD_LONG)
    
    # 检查与55均线的距离
    ma55_distance_ok = True
    ma55_distance_pct = 0
    
    if ma55_value is not None:
        ma55_distance_pct = abs(current_price - ma55_value) / ma55_value
        ma55_distance_ok = ma55_distance_pct <= Config.MA55_DISTANCE_THRESHOLD
    
    # 检查价格与红白线的距离
    red_line_distance_ok = True
    white_line_distance_ok = True
    red_line_distance_pct = 0
    white_line_distance_pct = 0
    
    # 计算与红线的距离（用于做多）
    if red_line is not None and not pd.isna(red_line):
        red_line_distance_pct = (current_price - red_line) / red_line
        red_line_distance_ok = red_line_distance_pct <= Config.RED_LINE_DISTANCE_THRESHOLD
    
    # 计算与白线的距离（用于做空）
    if white_line is not None and not pd.isna(white_line):
        white_line_distance_pct = (white_line - current_price) / white_line
        white_line_distance_ok = white_line_distance_pct <= Config.WHITE_LINE_DISTANCE_THRESHOLD
    
    # ===== 新增：转换确认增强条件 =====
    transition_strength_ok = True
    
    if transition_long:  # 白线转红线时的增强确认
        # 计算与前白线的突破强度，而非与新红线的距离
        price_strength_ok = (prev_white is not None and not pd.isna(prev_white) and 
                           (current_price / prev_white - 1) >= Config.PRICE_STRENGTH_THRESHOLD)
        
        # 打印正确的突破强度信息
        if prev_white is not None and not pd.isna(prev_white):
            print(f"价格强度检查: {'通过' if price_strength_ok else '不通过'} (价格高于原白线{(current_price / prev_white - 1)*100:.2f}%)")
        else:
            print("价格强度检查: 不通过 (前白线数据缺失)")
        
        # 2. 检查成交量强度 - 要求转换时成交量明显放大
        volume_strength_ok = volume_data.iloc[-1] > volume_data.iloc[-3:].mean() * Config.VOLUME_STRENGTH_THRESHOLD
        
        # 3. 检查K线形态 - 要求为阳线且收盘接近最高点(实体占比大)
        if len(volume_data.index) > 0 and hasattr(volume_data, 'index'):
            try:
                last_idx = volume_data.index[-1]
                # 修正：按迅投知识库API要求，传递位置参数，不用关键字参数
                now = time.strftime('%Y%m%d%H%M%S', time.localtime())
                candle_data = ContextInfo.get_market_data(
                    ['open', 'high', 'low', 'close'],
                    [g.code],
                    '5m',
                    now,   # start_time
                    now,   # end_time
                    False, # is_adjusted
                    '',    # adjustment_type
                    '',    # price_type
                    1      # count
                )
                if not candle_data.empty:
                    open_price = candle_data['open'].iloc[-1]
                    high_price = candle_data['high'].iloc[-1]
                    close_price = candle_data['close'].iloc[-1]
                    
                    # 是阳线
                    is_bullish = close_price > open_price
                    # 实体占比大
                    body_ratio = (close_price - open_price) / (high_price - open_price) if high_price > open_price else 0
                    candle_strength_ok = is_bullish and body_ratio > Config.CANDLE_BODY_RATIO
                else:
                    candle_strength_ok = True
            except Exception as e:
                print(f"检查K线形态出错: {e}")
                candle_strength_ok = True
        else:
            candle_strength_ok = True
            
        # 综合判断转换强度
        transition_strength_ok = price_strength_ok and (volume_strength_ok or candle_strength_ok)
        
        # 打印转换强度判断结果
        if transition_long:
            print("\n=== 转换强度增强检查 ===")
            print(f"价格强度检查: {'通过' if price_strength_ok else '不通过'} (价格高于原白线{(current_price / prev_white - 1)*100:.2f}%)")
            print(f"成交量强度检查: {'通过' if volume_strength_ok else '不通过'}")
            print(f"K线形态检查: {'通过' if candle_strength_ok else '不通过'}")
            print(f"转换强度总体检查: {'通过' if transition_strength_ok else '不通过'}")
    
    # ===== 新增部分结束 =====
    
    # 判断做多条件 - 添加转换强度确认
    can_long = (transition_long and  # 白线转红线
                price_above_ma and   # 价格在MA上方
                trend_up and         # 趋势向上
                volume_confirmed and # 成交量确认
                ma55_distance_ok and # 与55均线距离适中
                red_line_distance_ok and # 与红线距离适中
                transition_strength_ok and # 新增：转换强度确认
                time_filter)         # 时间过滤
    
    # 判断做空条件 - 空仓条件暂不修改
    can_short = (transition_short and  # 红线转白线
                 not price_above_ma and # 价格在MA下方
                 trend_down and        # 趋势向下
                 volume_confirmed and  # 成交量确认
                 ma55_distance_ok and  # 与55均线距离适中
                 white_line_distance_ok and # 与白线距离适中
                 time_filter)          # 时间过滤
    
    # 输出交易信号信息
    if transition_long or transition_short:
        print("\n=== 转换信号交易条件检查 ===")
        print(f"转换信号: {'白转红' if transition_long else '红转白' if transition_short else '无'}")
        print(f"价格位置: {'MA上方' if price_above_ma else 'MA下方'}")
        print(f"趋势方向: {'向上' if trend_up else '向下' if trend_down else '无明显趋势'}")
        print(f"成交量确认: {'是' if volume_confirmed else '否'}")
        print(f"时间过滤: {'是' if time_filter else '否'}")
        
        # 添加55均线距离信息
        if ma55_value is not None:
            print(f"与55均线距离: {ma55_distance_pct*100:.2f}% {'(符合要求)' if ma55_distance_ok else '(超出阈值)'}")
        
        # 添加红白线距离信息
        if red_line is not None and not pd.isna(red_line):
            print(f"价格与红线距离: {red_line_distance_pct*100:.2f}% {'(符合要求)' if red_line_distance_ok else '(超出阈值)'}")
        if white_line is not None and not pd.isna(white_line):
            print(f"价格与白线距离: {white_line_distance_pct*100:.2f}% {'(符合要求)' if white_line_distance_ok else '(超出阈值)'}")
        
        if red_line:
            print(f"价格与红线关系: {'价格在红线上方' if current_price > red_line else '价格在红线下方'}")
        if white_line:
            print(f"价格与白线关系: {'价格在白线上方' if current_price > white_line else '价格在白线下方'}")
        print(f"可以做多: {can_long}")
        print(f"可以做空: {can_short}")
        
        # 添加警告信息
        if transition_long and current_price < red_line:
            print("警告：白线转红线但价格在红线下方，建议观望")
        if transition_short and current_price > white_line:
            print("警告：红线转白线但价格在白线上方，建议观望")
        if not ma55_distance_ok:
            print(f"警告：价格偏离55均线过远({ma55_distance_pct*100:.2f}%)，建议等待回归后入场")
        if not red_line_distance_ok and transition_long:
            print(f"警告：价格偏离红线支撑过远({red_line_distance_pct*100:.2f}%)，建议等待回调后入场")
        if not white_line_distance_ok and transition_short:
            print(f"警告：价格偏离白线压力过远({white_line_distance_pct*100:.2f}%)，建议等待反弹后入场")
        # 新增警告
        if transition_long and not transition_strength_ok:
            print("警告：白线转红线强度不足，可能是假突破，建议观望")
    
    return can_long, can_short

def check_price_line_distance(current_price, white_line, red_line):
    """
    检查价格与红白线的距离
    
    参数:
    current_price: 当前价格
    white_line: 白线值
    red_line: 红线值
    
    返回:
    (red_line_distance_ok, white_line_distance_ok): 是否符合做多和做空的距离要求
    """
    red_line_distance_ok = True
    white_line_distance_ok = True
    
    # 检查价格与白线的距离（用于做空）
    if white_line is not None and not pd.isna(white_line):
        white_line_distance = (white_line - current_price) / white_line * 100
        white_line_distance_ok = white_line_distance <= Config.WHITE_LINE_DISTANCE_THRESHOLD * 100
        print(f"价格与白线距离: {white_line_distance:.2f}% {'(符合做空条件)' if white_line_distance_ok else '(超出做空阈值)'}")
    else:
        print("白线不存在或为空，无法计算与白线的距离")
        
    # 检查价格与红线的距离（用于做多）
    if red_line is not None and not pd.isna(red_line):
        red_line_distance = (current_price - red_line) / red_line * 100
        red_line_distance_ok = red_line_distance <= Config.RED_LINE_DISTANCE_THRESHOLD * 100
        print(f"价格与红线距离: {red_line_distance:.2f}% {'(符合做多条件)' if red_line_distance_ok else '(超出做多阈值)'}")
    else:
        print("红线不存在或为空，无法计算与红线的距离")
        
    return red_line_distance_ok, white_line_distance_ok

def check_and_print_line_distance(current_price, white_line, red_line):
    """
    检查并打印价格与红白线的距离
    
    参数:
    current_price: 当前价格
    white_line: 白线值
    red_line: 红线值
    
    返回:
    (red_line_distance_ok, white_line_distance_ok): 距离是否符合要求
    """
    red_line_distance_ok = True
    white_line_distance_ok = True
    
    # 减少调试信息输出
    # print("\n=== 价格与红白线距离检查 ===")
    # print(f"当前价格: {current_price}")
    # 
    # # 检查价格与白线的距离（用于做空）
    # if white_line is not None and not pd.isna(white_line):
    #     white_line_distance = (white_line - current_price) / white_line * 100
    #     white_line_distance_ok = white_line_distance <= Config.WHITE_LINE_DISTANCE_THRESHOLD * 100
    #     print(f"白线价格: {white_line}")
    #     print(f"价格与白线距离: {white_line_distance:.2f}% {'(符合做空条件)' if white_line_distance_ok else '(超出做空阈值)'}")
    #     if not white_line_distance_ok:
    #         print(f"警告: 价格与白线距离超过{Config.WHITE_LINE_DISTANCE_THRESHOLD * 100:.2f}%阈值，建议等待反弹后再考虑做空")
    # else:
    #     print("白线不存在或为空，无法计算距离")
    # 
    # # 检查价格与红线的距离（用于做多）
    # if red_line is not None and not pd.isna(red_line):
    #     red_line_distance = (current_price - red_line) / red_line * 100
    #     red_line_distance_ok = red_line_distance <= Config.RED_LINE_DISTANCE_THRESHOLD * 100
    #     print(f"红线价格: {red_line}")
    #     print(f"价格与红线距离: {red_line_distance:.2f}% {'(符合做多条件)' if red_line_distance_ok else '(超出做多阈值)'}")
    #     if not red_line_distance_ok:
    #         print(f"警告: 价格与红线距离超过{Config.RED_LINE_DISTANCE_THRESHOLD * 100:.2f}%阈值，建议等待回调后再考虑做多")
    # else:
    #     print("红线不存在或为空，无法计算距离")
    
    return red_line_distance_ok, white_line_distance_ok

def enhanced_stop_loss_check(ContextInfo, current_price):
    """
    增强版止损检查
    
    参数:
    ContextInfo: 上下文信息
    current_price: 当前价格
    
    返回:
    should_stop: 是否应该止损
    stop_price: 止损价格
    stop_reason: 止损原因
    """
    try:
        # ===== 新增：开仓ATR止损保护期逻辑 =====
        protect_seconds = getattr(Config, 'ATR_STOP_PROTECT_MINUTES', 7) * 60
        now = time.time()
        if hasattr(g, 'open_time') and g.open_time > 0:
            if now - g.open_time < protect_seconds:
                skip_atr_stop = True
            else:
                skip_atr_stop = False
        else:
            skip_atr_stop = False
        price_1m = ContextInfo.get_market_data(['open', 'high', 'low', 'close', 'volume'], 
                                           [g.code], 
                                           period='1m',
                                           count=20)
        price_5m = ContextInfo.get_market_data(['open', 'high', 'low', 'close', 'volume'], 
                                           [g.code], 
                                           period='5m',
                                           count=10)
        white_line_5m, red_line_5m = calculate_red_white_lines_exact(price_5m)
        # ====== 计算三重止损线 ======
        # 1. 红线止损
        red_line = None
        if red_line_5m.iloc[-1] is not None and not pd.isna(red_line_5m.iloc[-1]):
            red_line = red_line_5m.iloc[-1]
        # 2. ATR止损
        atr = calculate_atr(ContextInfo, period=10)
        atr_stop_multiple = getattr(Config, 'ATR_STOP_MULTIPLE', 1.0)
        atr_stop_price = g.open_price - atr * atr_stop_multiple
        # 3. 最大亏损止损
        max_loss_price = None
        if getattr(Config, 'ENABLE_MAX_LOSS_STOP', True):
            max_loss_points = getattr(Config, 'MAX_LOSS_POINTS', 15)
            max_loss_price = g.open_price - max_loss_points
        # ====== 新增：近距离只用红线止损 ======
        no_atr_stop = False
        if red_line is not None and g.open_price > 0:
            red_line_distance = abs(g.open_price - red_line) / red_line
            if red_line_distance < getattr(Config, 'NO_ATR_STOP_DISTANCE', 0.003):
                no_atr_stop = True
        # ====== 取三者中价格最高的那一条 ======
        stop_candidates = []
        stop_reasons = []
        if red_line is not None:
            stop_candidates.append(red_line)
            stop_reasons.append(f'价格跌破红线({red_line:.2f})')
        # ===== 新增：保护期内或近距离不加入ATR止损 =====
        if not skip_atr_stop and not no_atr_stop and atr is not None and not pd.isna(atr):
            stop_candidates.append(atr_stop_price)
            stop_reasons.append(f'价格跌破ATR止损线({atr_stop_price:.2f})')
        if max_loss_price is not None:
            stop_candidates.append(max_loss_price)
            stop_reasons.append(f'价格跌破最大亏损止损线({max_loss_price:.2f})')
        if not stop_candidates:
            return False, None, None
        stop_line = max(stop_candidates)
        stop_reason = stop_reasons[stop_candidates.index(stop_line)]
        # ====== 成交量确认 ======
        volume_confirmed = False
        if len(price_1m) >= 3:
            volume_ratio = price_1m['volume'].iloc[-1] / max(price_1m['volume'].iloc[-2], 1)
            if volume_ratio > 1.2:
                volume_confirmed = True
        if current_price < stop_line and volume_confirmed:
            return True, stop_line, stop_reason
        return False, None, None
    except Exception as e:
        print(f"增强版止损检查出错: {str(e)}")
        return False, None, None

def real_time_stop_loss_monitor(ContextInfo):
    """
    实时止损监控
    
    参数:
    ContextInfo: 上下文信息
    """
    try:
        # 获取当前持仓
        if g.position != "long":
            return
            
        # 获取当前价格
        current_price = ContextInfo.get_market_data(['close'], 
                                                [g.code], 
                                                period='1m',
                                                count=1)['close'].iloc[-1]
        
        # 执行增强版止损检查
        should_stop, stop_price, stop_reason = enhanced_stop_loss_check(ContextInfo, current_price)
        
        if should_stop:
            print(f"触发止损: {stop_reason}, 价格={stop_price}")
            
            # 执行止损
            passorder(7, 1101, account, g.code, 14, 0, g.position_size, '', 1, '多单止损', ContextInfo)
            log_trade("多单止损", current_price)
            
            # 更新持仓状态
            g.position = "none"
            g.position_size = 0
            sync_position_status(ContextInfo)
            
            # 记录平仓时间
            g.last_close_time = time.time()
            
    except Exception as e:
        print(f"实时止损监控出错: {str(e)}")

def check_stop_loss_and_reverse(ContextInfo, current_price, last_position_type=None):
    try:
        # 减少调试信息输出
        # print("\n=== 检查止损和反向开仓 ===")
        # print(f"当前持仓: {g.position}")
        # print(f"当前价格: {current_price}")
        
        # 获取5分钟K线数据
        price_5m = ContextInfo.get_market_data(['open', 'high', 'low', 'close', 'volume'], 
                                           [g.code], 
                                           period='5m',
                                           count=10)
        
        # 计算红白线
        white_line_5m, red_line_5m = calculate_red_white_lines_exact(price_5m)
        
        # 多单止损并反手做空
        if g.position == "long":
            # 检查是否跌破红线
            red_line_broken = False
            
            # 情况1：红线消失且价格低于前一根红线
            if red_line_5m.iloc[-2] is not None and not pd.isna(red_line_5m.iloc[-2]) and (red_line_5m.iloc[-1] is None or pd.isna(red_line_5m.iloc[-1])):
                if price_5m['close'].iloc[-1] < red_line_5m.iloc[-2]:
                    red_line_broken = True
                    print("多单止损信号：检测到跌破红线支撑（红线消失）")
            
            # 情况2：价格低于当前红线
            elif red_line_5m.iloc[-1] is not None and not pd.isna(red_line_5m.iloc[-1]):
                if price_5m['close'].iloc[-1] < red_line_5m.iloc[-1]:
                    red_line_broken = True
                    print(f"多单止损信号：检测到跌破红线支撑（价格{price_5m['close'].iloc[-1]}低于红线{red_line_5m.iloc[-1]}）")
            
            if red_line_broken:
                # 1. 先平多仓
                last_position_type = g.position
                passorder(7, 1101, account, g.code, 14, 0, g.position_size, '', 1, '多单止损', ContextInfo)
                log_trade("多单止损", current_price)
                
                # 记录平仓时间
                g.last_close_time = time.time()
                
                # 2. 直接开空仓
                passorder(3, 1101, account, g.code, 14, 0, Config.TRADE_HANDS, '', 1, '反手开空', ContextInfo)
                log_trade("反手开空", current_price)
                
                # 3. 更新状态
                g.position = "short"
                g.buy_short += 1
                g.trace_time_short = time.time()
                g.hold_price = current_price
                g.open_price = current_price
                g.position_size = Config.TRADE_HANDS
                g.sysid = {}
                
                # 4. 重置止盈标记
                g.profit_taken_1_short = False
                g.profit_taken_2_short = False
                g.profit_taken_3_short = False
                
                # 在触发止损和反向开仓时添加更详细的日志
                print(f"触发止损和反向开仓:")
                print(f"- 原持仓方向: long")
                print(f"- 新持仓方向: short")
                print(f"- 开仓价格: {current_price}")
                
                return True
                
        # 空单止损并反手做多
        elif g.position == "short":
            # 检查是否突破白线
            white_line_broken = False
            
            # 情况1：白线消失且价格高于前一根白线
            if white_line_5m.iloc[-2] is not None and not pd.isna(white_line_5m.iloc[-2]) and (white_line_5m.iloc[-1] is None or pd.isna(white_line_5m.iloc[-1])):
                if price_5m['close'].iloc[-1] > white_line_5m.iloc[-2]:
                    white_line_broken = True
                    print("空单止损信号：检测到突破白线压力（白线消失）")
            
            # 情况2：价格高于当前白线
            elif white_line_5m.iloc[-1] is not None and not pd.isna(white_line_5m.iloc[-1]):
                if price_5m['close'].iloc[-1] > white_line_5m.iloc[-1]:
                    white_line_broken = True
                    print(f"空单止损信号：检测到突破白线压力（价格{price_5m['close'].iloc[-1]}高于白线{white_line_5m.iloc[-1]}）")
            
            if white_line_broken:
                # 1. 先平空仓
                last_position_type = g.position
                passorder(9, 1101, account, g.code, 14, 0, g.position_size, '', 1, '空单止损', ContextInfo)
                log_trade("空单止损", current_price)
                
                # 记录平仓时间
                g.last_close_time = time.time()
                
                # 2. 直接开多仓
                passorder(0, 1101, account, g.code, 14, 0, Config.TRADE_HANDS, '', 1, '反手开多', ContextInfo)
                log_trade("反手开多", current_price)
                
                # 3. 更新状态
                g.position = "long"
                g.buy_long += 1
                g.trace_time_long = time.time()
                g.hold_price = current_price
                g.open_price = current_price
                g.position_size = Config.TRADE_HANDS
                g.sysid = {}
                
                # 4. 重置止盈标记
                g.profit_taken_1 = False
                g.profit_taken_2 = False
                g.profit_taken_3 = False
                
                # 在触发止损和反向开仓时添加更详细的日志
                print(f"触发止损和反向开仓:")
                print(f"- 原持仓方向: short")
                print(f"- 新持仓方向: long")
                print(f"- 开仓价格: {current_price}")
                
                return True
                
        return False
        
    except Exception as e:
        print(f"止损和反向开仓检查出错: {str(e)}")
        return False

# 新增获取参数的函数
def get_atr_stop_multiple(code):
    prefix = code[:2].upper() if code else ''
    return Config.ATR_STOP_MULTIPLE_MAP.get(prefix, Config.ATR_STOP_MULTIPLE)

def get_min_stop_distance(code):
    prefix = code[:2].upper() if code else ''
    return Config.MIN_STOP_DISTANCE_MAP.get(prefix, Config.MIN_STOP_DISTANCE)

# ====== 新增获取分品种止盈参数的函数 ======
def get_take_profit_atr_1(code):
    prefix = code[:2].upper() if code else ''
    return Config.TAKE_PROFIT_ATR_1_MAP.get(prefix, Config.DEFAULT_TAKE_PROFIT_ATR_1)

def get_min_take_profit_points(code):
    prefix = code[:2].upper() if code else ''
    return Config.MIN_TAKE_PROFIT_POINTS_MAP.get(prefix, Config.DEFAULT_MIN_TAKE_PROFIT_POINTS)
# ====== End 新增函数 ======

def enhanced_stop_loss_check_short(ContextInfo, current_price):
    """
    增强版止损检查（空单方向）
    参数:
    ContextInfo: 上下文信息
    current_price: 当前价格
    返回:
    should_stop: 是否应该止损
    stop_price: 止损价格
    stop_reason: 止损原因
    """
    try:
        # ===== 新增：开仓ATR止损保护期逻辑 =====
        protect_seconds = getattr(Config, 'ATR_STOP_PROTECT_MINUTES', 7) * 60
        now = time.time()
        if hasattr(g, 'open_time') and g.open_time > 0:
            if now - g.open_time < protect_seconds:
                skip_atr_stop = True
            else:
                skip_atr_stop = False
        else:
            skip_atr_stop = False
        price_1m = ContextInfo.get_market_data(['open', 'high', 'low', 'close', 'volume'], 
                                           [g.code], 
                                           period='1m',
                                           count=20)
        price_5m = ContextInfo.get_market_data(['open', 'high', 'low', 'close', 'volume'], 
                                           [g.code], 
                                           period='5m',
                                           count=10)
        white_line_5m, red_line_5m = calculate_red_white_lines_exact(price_5m)
        # ====== 计算三重止损线 ======
        # 1. 白线止损
        white_line = None
        if white_line_5m.iloc[-1] is not None and not pd.isna(white_line_5m.iloc[-1]):
            white_line = white_line_5m.iloc[-1]
        # 2. ATR止损
        atr = calculate_atr(ContextInfo, period=10)
        atr_stop_multiple = getattr(Config, 'ATR_STOP_MULTIPLE', 1.0)
        atr_stop_price = g.open_price + atr * atr_stop_multiple
        # 3. 最大亏损止损
        max_loss_price = None
        if getattr(Config, 'ENABLE_MAX_LOSS_STOP', True):
            max_loss_points = getattr(Config, 'MAX_LOSS_POINTS', 15)
            max_loss_price = g.open_price + max_loss_points
        # ====== 新增：近距离只用白线止损 ======
        no_atr_stop = False
        if white_line is not None and g.open_price > 0:
            white_line_distance = abs(white_line - g.open_price) / white_line
            if white_line_distance < getattr(Config, 'NO_ATR_STOP_DISTANCE', 0.003):
                no_atr_stop = True
        # ====== 取三者中价格最低的那一条 ======
        stop_candidates = []
        stop_reasons = []
        if white_line is not None:
            stop_candidates.append(white_line)
            stop_reasons.append(f'价格突破白线({white_line:.2f})')
        # ===== 新增：保护期内或近距离不加入ATR止损 =====
        if not skip_atr_stop and not no_atr_stop and atr is not None and not pd.isna(atr):
            stop_candidates.append(atr_stop_price)
            stop_reasons.append(f'价格突破ATR止损线({atr_stop_price:.2f})')
        if max_loss_price is not None:
            stop_candidates.append(max_loss_price)
            stop_reasons.append(f'价格突破最大亏损止损线({max_loss_price:.2f})')
        if not stop_candidates:
            return False, None, None
        stop_line = min(stop_candidates)
        stop_reason = stop_reasons[stop_candidates.index(stop_line)]
        # ====== 成交量确认 ======
        volume_confirmed = False
        if len(price_1m) >= 3:
            volume_ratio = price_1m['volume'].iloc[-1] / max(price_1m['volume'].iloc[-2], 1)
            if volume_ratio > 1.2:
                volume_confirmed = True
        if current_price > stop_line and volume_confirmed:
            return True, stop_line, stop_reason
        return False, None, None
    except Exception as e:
        print(f"增强版止损检查(空单)出错: {str(e)}")
        return False, None, None



def calculate_atr(ContextInfo, period=None):  # 5分钟ATR
    if period is None:
        period = Config.ATR_WINDOW_5M
    price_data = ContextInfo.get_market_data(['high', 'low', 'close'], 
                                         [g.code], 
                                         period='5m',   # 5分钟K线
                                         count=period+10)
    # 计算真实波动幅度
    tr1 = abs(price_data['high'] - price_data['low'])
    tr2 = abs(price_data['high'] - price_data['close'].shift(1))
    tr3 = abs(price_data['low'] - price_data['close'].shift(1))
    # 取三者最大值
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    # 计算ATR
    atr = tr.rolling(window=period).mean().iloc[-1]
    return atr

def check_pullback_volume(volume):
    """
    专门针对回调买入的成交量检查
    
    参数:
    volume: 成交量序列
    
    返回:
    True: 成交量符合回调买入要求
    False: 成交量不符合回调买入要求
    """
    # 回调阶段（前几根K线）成交量应该减少
    pullback_volume_decrease = volume.iloc[-3:-1].mean() < volume.iloc[-6:-3].mean()
    
    # 当前K线（反弹确认）成交量应该增加
    current_volume_increase = volume.iloc[-1] > volume.iloc[-2]
    
    # 综合判断
    return pullback_volume_decrease and current_volume_increase

def check_downtrend_volume(volume, close):
    """
    专门针对下跌行情的成交量检查
    
    参数:
    volume: 成交量序列
    close: 收盘价序列
    
    返回:
    True: 成交量符合下跌行情特征
    False: 成交量不符合下跌行情特征
    """
    # 计算价格变化
    price_change = close.pct_change()
    
    # 下跌确认
    is_declining = close.iloc[-1] < close.iloc[-3]
    
    # 情况1: 放量下跌 - 传统意义上的看空信号
    volume_increase = volume.iloc[-1] > volume.iloc[-2] * 1.2
    
    # 情况2: 缩量下跌 - 买盘撤离导致的下跌
    volume_decrease = volume.iloc[-1] < volume.iloc[-5:].mean()
    price_decline = price_change.iloc[-1] < -0.002  # 价格下跌超过0.2%
    
    # 情况3: 连续下跌 - 持续的下跌趋势
    continuous_decline = all(price_change.iloc[-3:] < 0)
    
    # 综合判断
    return is_declining and (volume_increase or (volume_decrease and price_decline) or continuous_decline)

def detect_line_transition(prev_white, curr_white, prev_red, curr_red):
    """
    检测红白线转换
    
    参数:
    prev_white: 前一根K线的白线
    curr_white: 当前K线的白线
    prev_red: 前一根K线的红线
    curr_red: 当前K线的红线
    
    返回:
    1: 白线转红线
    -1: 红线转白线
    0: 无转换
    """
    try:
        # 减少调试信息输出
        # print("\n=== 红白线转换检测 ===")
        # print(f"前一根K线白线: {prev_white}")
        # print(f"当前K线白线: {curr_white}")
        # print(f"前一根K线红线: {prev_red}")
        # print(f"当前K线红线: {curr_red}")
        # 
        # print(f"转换检测详情: 前白={prev_white}, 当白={curr_white}, 前红={prev_red}, 当红={curr_red}")
        
        # 白线转红线
        if (prev_white is not None and not pd.isna(prev_white) and 
            (curr_red is not None and not pd.isna(curr_red)) and 
            (curr_white is None or pd.isna(curr_white))):
            print(f"检测到标准白线转红线: {prev_white} -> {curr_red}")
            return 1
            
        # 红线转白线
        if (prev_red is not None and not pd.isna(prev_red) and 
            (curr_white is not None and not pd.isna(curr_white)) and 
            (curr_red is None or pd.isna(curr_red))):
            print(f"检测到标准红线转白线: {prev_red} -> {curr_white}")
            return -1
            
        return 0
    except Exception as e:
        print(f"检测红白线转换出错: {e}")
        return 0

# 在全局作用域中定义函数
def check_historical_transition(white_line, red_line):
    """
    检查历史数据中是否有白线转红线
    
    参数:
    white_line: 白线序列
    red_line: 红线序列
    
    返回:
    (has_transition, bars_ago): 是否有转换，以及发生在多少根K线之前
    """
    try:
        # 确保数据足够
        if white_line is None or red_line is None or len(white_line) < 5 or len(red_line) < 5:
            return False, 0
            
        # 从最近的数据开始向前查找
        for i in range(len(white_line)-2, 0, -1):
            # 检查是否有白线转红线
            if (not pd.isna(white_line.iloc[i]) and white_line.iloc[i] is not None and 
                pd.isna(white_line.iloc[i+1]) and 
                not pd.isna(red_line.iloc[i+1]) and red_line.iloc[i+1] is not None):
                
                # 计算发生在多少根K线之前
                bars_ago = len(white_line) - 1 - i
                print(f"检测到白线转红线发生在{bars_ago}根K线之前")
                return True, bars_ago
                
        return False, 0
    except Exception as e:
        print(f"检查历史转换出错: {e}")
        return False, 0

# 在handlebar函数中，在使用can_open_position之前添加定义

# 其他可能的限制条件
# ...

# 修改红白线检查函数
def check_break_red_line(price_data, red_line_prev, red_line_curr, current_price):
    """
    检查是否跌破红线
    
    参数:
    price_data: 价格数据
    red_line_prev: 前一根K线的红线
    red_line_curr: 当前K线的红线
    current_price: 当前价格
    
    返回:
    是否跌破红线
    """
    try:
        # 减少调试信息输出
        # print("=== 跌破红线详细检查 ===")
        # print(f"前一根K线红线: {red_line_prev}")
        # print(f"当前K线红线: {red_line_curr}")
        # 
        # if price_data is None or price_data.empty:
        #     print("价格数据为空")
        #     return False
        #     
        # current_price = price_data['close'].iloc[-1]
        # print(f"当前收盘价: {current_price}")
        # 
        # # 添加空值检查 - 红线
        # if red_line_prev is not None and not pd.isna(red_line_prev) and current_price is not None:
        #     price_prev_pct = (current_price - red_line_prev) / red_line_prev * 100
        #     print(f"价格与前一根红线比较: {price_prev_pct:.2f}%")
        # else:
        #     print("前一根红线不存在或为空，无法计算百分比")
        #     price_prev_pct = None
        #     
        # if red_line_curr is not None and not pd.isna(red_line_curr) and current_price is not None:
        #     price_curr_pct = (current_price - red_line_curr) / red_line_curr * 100
        #     print(f"价格与当前红线比较: {price_curr_pct:.2f}%")
        #     
        #     # 检查价格与红线的距离（用于做多）
        #     red_line_distance = (current_price - red_line_curr) / red_line_curr * 100
        #     red_line_distance_ok = red_line_distance <= Config.RED_LINE_DISTANCE_THRESHOLD * 100
        #     print(f"价格与红线距离: {red_line_distance:.2f}% {'(符合做多条件)' if red_line_distance_ok else '(超出做多阈值)'}")
        # else:
        #     print("当前红线不存在或为空，无法计算百分比")
        #     price_curr_pct = None
            
        # 获取5分钟白线值
        try:
            # 计算白线值
            white_line_data, _ = calculate_red_white_lines_exact(price_data)
            white_line_value = white_line_data.iloc[-1] if not white_line_data.empty else None
            
            # 如果白线存在，计算价格与白线的距离
            if white_line_value is not None and not pd.isna(white_line_value):
                white_line_distance = (white_line_value - current_price) / white_line_value * 100
                white_line_distance_ok = white_line_distance <= Config.WHITE_LINE_DISTANCE_THRESHOLD * 100
                print(f"价格与白线距离: {white_line_distance:.2f}% {'(符合做空条件)' if white_line_distance_ok else '(超出做空阈值)'}")
        except Exception as e:
            print(f"获取白线或计算白线距离时出错: {e}")
        
        # 判断是否跌破红线
        if red_line_prev is not None and not pd.isna(red_line_prev) and current_price < red_line_prev:
            return True
        if red_line_curr is not None and not pd.isna(red_line_curr) and current_price < red_line_curr:
            return True
            
        return False
    except Exception as e:
        print(f"检查红白线出错: {e}")
        return False

# 修改打印红白线详情函数
def print_line_details(price_data, white_line, red_line, current_price):
    """
    打印红白线详细信息
    
    参数:
    price_data: 价格数据
    white_line: 白线值
    red_line: 红线值
    current_price: 当前价格
    """
    try:
        print("\n=== 红白线详细信息 ===")
        
        if price_data is None or price_data.empty:
            print("价格数据为空")
            return
            
        print(f"当前价格: {current_price}")
        print(f"白线价格: {white_line}")
        print(f"红线价格: {red_line}")
        
        # 检查价格与白线的距离（用于做空）
        if white_line is not None and not pd.isna(white_line) and current_price is not None:
            white_line_distance = (white_line - current_price) / white_line * 100
            white_line_distance_ok = white_line_distance <= Config.WHITE_LINE_DISTANCE_THRESHOLD * 100
            print(f"价格与白线距离: {white_line_distance:.2f}% {'(符合做空条件)' if white_line_distance_ok else '(超出做空阈值)'}")
            
            # 检查是否突破白线
            if current_price > white_line:
                print(f"突破白线: 价格{current_price} > 白线{white_line}")
            else:
                print("无白线突破")
        else:
            print("无白线或白线为空")
            
        # 检查价格与红线的距离（用于做多）
        if red_line is not None and not pd.isna(red_line) and current_price is not None:
            red_line_distance = (current_price - red_line) / red_line * 100
            red_line_distance_ok = red_line_distance <= Config.RED_LINE_DISTANCE_THRESHOLD * 100
            print(f"价格与红线距离: {red_line_distance:.2f}% {'(符合做多条件)' if red_line_distance_ok else '(超出做多阈值)'}")
            
            # 检查是否跌破红线
            if current_price < red_line:
                print(f"跌破红线: 价格{current_price} < 红线{red_line}")
            else:
                print("无红线跌破")
        else:
            print("无红线或红线为空")
            
        # 打印最高价和最低价
        if 'high' in price_data.columns and 'low' in price_data.columns:
            print(f"最高价: {price_data['high'].iloc[-1]}")
            print(f"最低价: {price_data['low'].iloc[-1]}")
            
    except Exception as e:
        print(f"打印红白线详情出错: {e}")

# 添加新的红白线计算函数
def calculate_red_white_lines(price_data):
    """
    根据原始公式计算红白线指标
    
    参数:
    price_data: 包含open, high, low, close的DataFrame
    
    返回:
    white_line: 白线序列
    red_line: 红线序列
    """
    try:
        # 确保数据足够
        if len(price_data) < 10:
            return pd.Series([None] * len(price_data)), pd.Series([None] * len(price_data))
        
        # 计算高低点
        high = price_data['high']
        low = price_data['low']
        open_price = price_data['open']
        close = price_data['close']
        
        # 计算2根K线的最高价和最低价
        hx = high.rolling(window=2).max()
        lx = low.rolling(window=2).min()
        
        # 初始化结果序列
        white_line = pd.Series([None] * len(price_data), index=price_data.index)
        red_line = pd.Series([None] * len(price_data), index=price_data.index)
        k2 = pd.Series([0] * len(price_data), index=price_data.index)
        
        # 计算H1和L1
        for i in range(5, len(price_data)):
            # 白线条件
            if (lx[i] > lx[i-1] and lx[i] > lx[i-3] and lx[i] > lx[i-5] and 
                hx[i] > hx[i-1] and hx[i] > hx[i-2] and hx[i] > hx[i-4] and 
                open_price[i] < close[i] and 
                (close[i] - open_price.iloc[:i+1].min()) > 0):
                
                # 设置白线值为前4根K线的最低价
                white_line[i] = lx[i-4]
                k2[i] = 1
            
            # 红线条件
            elif (hx[i] < hx[i-1] and hx[i] < hx[i-2] and hx[i] < hx[i-4] and 
                  lx[i] < lx[i-1] and lx[i] < lx[i-3] and lx[i] < lx[i-5] and 
                  open_price[i] > close[i] and 
                  (open_price.iloc[:i+1].max() - close[i]) > 0):
                
                # 设置红线值为前4根K线的最高价
                red_line[i] = hx[i-4]
                k2[i] = -3
        
        # 填充空值 - 使用前一个有效值
        for i in range(1, len(price_data)):
            if k2[i] == 0:
                k2[i] = k2[i-1]
            
            if k2[i] == 1 and pd.isna(white_line[i]):
                # 找到前一个白线值
                for j in range(i-1, -1, -1):
                    if not pd.isna(white_line[j]):
                        white_line[i] = white_line[j]
                        break
            
            elif k2[i] == -3 and pd.isna(red_line[i]):
                # 找到前一个红线值
                for j in range(i-1, -1, -1):
                    if not pd.isna(red_line[j]):
                        red_line[i] = red_line[j]
                        break
        
        # 减少调试信息输出
        # print(f"红白线计算完成: 数据长度={len(price_data)}, 白线非空值={white_line.notna().sum()}, 红线非空值={red_line.notna().sum()}")
        return white_line, red_line
    
    except Exception as e:
        print(f"计算红白线出错: {e}")
        # 返回空序列
        return pd.Series([None] * len(price_data), index=price_data.index), pd.Series([None] * len(price_data), index=price_data.index)

def calculate_red_white_lines_exact(price_data):
    """
    严格按照文华指标源代码计算红白线
    
    参数:
    price_data: 包含open, high, low, close的DataFrame
    
    返回:
    white_line: 白线序列
    red_line: 红线序列
    """
    try:
        # 确保数据足够
        if len(price_data) < 10:
            return pd.Series([None] * len(price_data)), pd.Series([None] * len(price_data))
        
        # 计算HX和LX
        high = price_data['high']
        low = price_data['low']
        open_price = price_data['open']
        close = price_data['close']
        
        # HX:=HHV(HIGH,2);
        hx = high.rolling(window=2).max()
        
        # LX:=LLV(LOW,2);
        lx = low.rolling(window=2).min()
        
        # 初始化H1, L1, K1, K2, G
        h1 = pd.Series([0] * len(price_data), index=price_data.index)
        l1 = pd.Series([0] * len(price_data), index=price_data.index)
        k1 = pd.Series([0] * len(price_data), index=price_data.index)
        k2 = pd.Series([0] * len(price_data), index=price_data.index)
        g = pd.Series([None] * len(price_data), index=price_data.index)
        
        # 计算H1和L1
        for i in range(5, len(price_data)):
            # H1:=IFELSE(HX<REF(HX,1)&&HX<REF(HX,2)&&HX<REF(HX,4)&&LX<REF(LX,1)&&LX<REF(LX,3)&&LX<REF(LX,5)&&OPEN>CLOSE&&(HHV(OPEN,0)-CLOSE)>0,REF(HX,4),0);
            if (hx[i] < hx[i-1] and hx[i] < hx[i-2] and hx[i] < hx[i-4] and 
                lx[i] < lx[i-1] and lx[i] < lx[i-3] and lx[i] < lx[i-5] and 
                open_price[i] > close[i] and 
                (open_price.iloc[:i+1].max() - close[i]) > 0):
                h1[i] = hx[i-4]
            else:
                h1[i] = 0
            
            # L1:=IFELSE(LX>REF(LX,1)&&LX>REF(LX,3)&&LX>REF(LX,5)&&HX>REF(HX,1)&&HX>REF(HX,2)&&HX>REF(HX,4)&&OPEN<CLOSE&&(CLOSE-LLV(OPEN,0))>0,REF(LX,4),0);
            if (lx[i] > lx[i-1] and lx[i] > lx[i-3] and lx[i] > lx[i-5] and 
                hx[i] > hx[i-1] and hx[i] > hx[i-2] and hx[i] > hx[i-4] and 
                open_price[i] < close[i] and 
                (close[i] - open_price.iloc[:i+1].min()) > 0):
                l1[i] = lx[i-4]
            else:
                l1[i] = 0
        
        # H2:=VALUEWHEN(H1>0,H1);
        h2 = pd.Series([None] * len(price_data), index=price_data.index)
        last_valid_h1 = None
        for i in range(len(price_data)):
            if h1[i] > 0:
                last_valid_h1 = h1[i]
            h2[i] = last_valid_h1
        
        # L2:=VALUEWHEN(L1>0,L1);
        l2 = pd.Series([None] * len(price_data), index=price_data.index)
        last_valid_l1 = None
        for i in range(len(price_data)):
            if l1[i] > 0:
                last_valid_l1 = l1[i]
            l2[i] = last_valid_l1
        
        # K1:=IFELSE(CLOSE>H2,-3,IFELSE(CLOSE<L2,1,0));
        for i in range(len(price_data)):
            if h2[i] is not None and close[i] > h2[i]:
                k1[i] = -3
            elif l2[i] is not None and close[i] < l2[i]:
                k1[i] = 1
            else:
                k1[i] = 0
        
        # K2:=VALUEWHEN(K1<>0,K1);
        last_valid_k1 = 0
        for i in range(len(price_data)):
            if k1[i] != 0:
                last_valid_k1 = k1[i]
            k2[i] = last_valid_k1
        
        # G:=IFELSE(K2=1,H2,L2);
        for i in range(len(price_data)):
            if k2[i] == 1:
                g[i] = h2[i]
            else:
                g[i] = l2[i]
        
        # 转换为红白线
        white_line = pd.Series([None] * len(price_data), index=price_data.index)
        red_line = pd.Series([None] * len(price_data), index=price_data.index)
        
        for i in range(len(price_data)):
            if k2[i] == 1:  # 白线
                white_line[i] = g[i]
                red_line[i] = None
            elif k2[i] == -3:  # 红线
                red_line[i] = g[i]
                white_line[i] = None
        
        # 减少调试信息输出
        # print(f"文华指标红白线计算完成: 数据长度={len(price_data)}, 白线非空值={white_line.notna().sum()}, 红线非空值={red_line.notna().sum()}")
        return white_line, red_line
    
    except Exception as e:
        print(f"计算文华指标红白线出错: {e}")
        # 返回空序列
        return pd.Series([None] * len(price_data), index=price_data.index), pd.Series([None] * len(price_data), index=price_data.index)

def should_trade_with_transition(transition_signal, current_price, MA_Line, trend, volume_data, 
                               white_line=None, red_line=None, time_filter=True, ma55_value=None,
                               prev_white=None, prev_red=None):  # 添加前一根K线的线值
    
    # 解析转换信号
    transition_long = transition_signal == 1  # 白线转红线，做多信号
    transition_short = transition_signal == -1  # 红线转白线，做空信号
    
    # 价格与MA的关系
    price_above_ma = current_price > MA_Line
    
    # 趋势方向
    trend_up = trend == 1
    trend_down = trend == -1
    
    # 成交量确认
    volume_confirmed = check_volume_increase(volume_data, Config.VOLUME_THRESHOLD_LONG)
    
    # 检查与55均线的距离
    ma55_distance_ok = True
    ma55_distance_pct = 0
    
    if ma55_value is not None:
        ma55_distance_pct = abs(current_price - ma55_value) / ma55_value
        ma55_distance_ok = ma55_distance_pct <= Config.MA55_DISTANCE_THRESHOLD
    
    # 检查价格与红白线的距离
    red_line_distance_ok = True
    white_line_distance_ok = True
    red_line_distance_pct = 0
    white_line_distance_pct = 0
    
    # 计算与红线的距离（用于做多）
    if red_line is not None and not pd.isna(red_line):
        red_line_distance_pct = (current_price - red_line) / red_line
        red_line_distance_ok = red_line_distance_pct <= Config.RED_LINE_DISTANCE_THRESHOLD
    
    # 计算与白线的距离（用于做空）
    if white_line is not None and not pd.isna(white_line):
        white_line_distance_pct = (white_line - current_price) / white_line
        white_line_distance_ok = white_line_distance_pct <= Config.WHITE_LINE_DISTANCE_THRESHOLD
    
    # ===== 新增：转换确认增强条件 =====
    transition_strength_ok = True
    
    if transition_long:  # 白线转红线时的增强确认
        # 计算与前白线的突破强度，而非与新红线的距离
        price_strength_ok = (prev_white is not None and not pd.isna(prev_white) and 
                           (current_price / prev_white - 1) >= Config.PRICE_STRENGTH_THRESHOLD)
        
        # 打印正确的突破强度信息
        if prev_white is not None and not pd.isna(prev_white):
            print(f"价格强度检查: {'通过' if price_strength_ok else '不通过'} (价格高于原白线{(current_price / prev_white - 1)*100:.2f}%)")
        else:
            print("价格强度检查: 不通过 (前白线数据缺失)")
        
        # 2. 检查成交量强度 - 要求转换时成交量明显放大
        volume_strength_ok = volume_data.iloc[-1] > volume_data.iloc[-3:].mean() * Config.VOLUME_STRENGTH_THRESHOLD
        
        # 3. 检查K线形态 - 要求为阳线且收盘接近最高点(实体占比大)
        if len(volume_data.index) > 0 and hasattr(volume_data, 'index'):
            try:
                last_idx = volume_data.index[-1]
                # 修正：按迅投知识库API要求，传递位置参数，不用关键字参数
                now = time.strftime('%Y%m%d%H%M%S', time.localtime())
                candle_data = ContextInfo.get_market_data(
                    ['open', 'high', 'low', 'close'],
                    [g.code],
                    '5m',
                    now,   # start_time
                    now,   # end_time
                    False, # is_adjusted
                    '',    # adjustment_type
                    '',    # price_type
                    1      # count
                )
                if not candle_data.empty:
                    open_price = candle_data['open'].iloc[-1]
                    high_price = candle_data['high'].iloc[-1]
                    close_price = candle_data['close'].iloc[-1]
                    
                    # 是阳线
                    is_bullish = close_price > open_price
                    # 实体占比大
                    body_ratio = (close_price - open_price) / (high_price - open_price) if high_price > open_price else 0
                    candle_strength_ok = is_bullish and body_ratio > Config.CANDLE_BODY_RATIO
                else:
                    candle_strength_ok = True
            except Exception as e:
                print(f"检查K线形态出错: {e}")
                candle_strength_ok = True
        else:
            candle_strength_ok = True
            
        # 综合判断转换强度
        transition_strength_ok = price_strength_ok and (volume_strength_ok or candle_strength_ok)
        
        # 打印转换强度判断结果
        if transition_long:
            print("\n=== 转换强度增强检查 ===")
            print(f"价格强度检查: {'通过' if price_strength_ok else '不通过'} (价格高于原白线{(current_price / prev_white - 1)*100:.2f}%)")
            print(f"成交量强度检查: {'通过' if volume_strength_ok else '不通过'}")
            print(f"K线形态检查: {'通过' if candle_strength_ok else '不通过'}")
            print(f"转换强度总体检查: {'通过' if transition_strength_ok else '不通过'}")
    
    # ===== 新增部分结束 =====
    
    # 判断做多条件 - 添加转换强度确认
    can_long = (transition_long and  # 白线转红线
                price_above_ma and   # 价格在MA上方
                trend_up and         # 趋势向上
                volume_confirmed and # 成交量确认
                ma55_distance_ok and # 与55均线距离适中
                red_line_distance_ok and # 与红线距离适中
                transition_strength_ok and # 新增：转换强度确认
                time_filter)         # 时间过滤
    
    # 判断做空条件 - 空仓条件暂不修改
    can_short = (transition_short and  # 红线转白线
                 not price_above_ma and # 价格在MA下方
                 trend_down and        # 趋势向下
                 volume_confirmed and  # 成交量确认
                 ma55_distance_ok and  # 与55均线距离适中
                 white_line_distance_ok and # 与白线距离适中
                 time_filter)          # 时间过滤
    
    # 输出交易信号信息
    if transition_long or transition_short:
        print("\n=== 转换信号交易条件检查 ===")
        print(f"转换信号: {'白转红' if transition_long else '红转白' if transition_short else '无'}")
        print(f"价格位置: {'MA上方' if price_above_ma else 'MA下方'}")
        print(f"趋势方向: {'向上' if trend_up else '向下' if trend_down else '无明显趋势'}")
        print(f"成交量确认: {'是' if volume_confirmed else '否'}")
        print(f"时间过滤: {'是' if time_filter else '否'}")
        
        # 添加55均线距离信息
        if ma55_value is not None:
            print(f"与55均线距离: {ma55_distance_pct*100:.2f}% {'(符合要求)' if ma55_distance_ok else '(超出阈值)'}")
        
        # 添加红白线距离信息
        if red_line is not None and not pd.isna(red_line):
            print(f"价格与红线距离: {red_line_distance_pct*100:.2f}% {'(符合要求)' if red_line_distance_ok else '(超出阈值)'}")
        if white_line is not None and not pd.isna(white_line):
            print(f"价格与白线距离: {white_line_distance_pct*100:.2f}% {'(符合要求)' if white_line_distance_ok else '(超出阈值)'}")
        
        if red_line:
            print(f"价格与红线关系: {'价格在红线上方' if current_price > red_line else '价格在红线下方'}")
        if white_line:
            print(f"价格与白线关系: {'价格在白线上方' if current_price > white_line else '价格在白线下方'}")
        print(f"可以做多: {can_long}")
        print(f"可以做空: {can_short}")
        
        # 添加警告信息
        if transition_long and current_price < red_line:
            print("警告：白线转红线但价格在红线下方，建议观望")
        if transition_short and current_price > white_line:
            print("警告：红线转白线但价格在白线上方，建议观望")
        if not ma55_distance_ok:
            print(f"警告：价格偏离55均线过远({ma55_distance_pct*100:.2f}%)，建议等待回归后入场")
        if not red_line_distance_ok and transition_long:
            print(f"警告：价格偏离红线支撑过远({red_line_distance_pct*100:.2f}%)，建议等待回调后入场")
        if not white_line_distance_ok and transition_short:
            print(f"警告：价格偏离白线压力过远({white_line_distance_pct*100:.2f}%)，建议等待反弹后入场")
        # 新增警告
        if transition_long and not transition_strength_ok:
            print("警告：白线转红线强度不足，可能是假突破，建议观望")
    
    return can_long, can_short

def check_price_line_distance(current_price, white_line, red_line):
    """
    检查价格与红白线的距离
    
    参数:
    current_price: 当前价格
    white_line: 白线值
    red_line: 红线值
    
    返回:
    (red_line_distance_ok, white_line_distance_ok): 是否符合做多和做空的距离要求
    """
    red_line_distance_ok = True
    white_line_distance_ok = True
    
    # 检查价格与白线的距离（用于做空）
    if white_line is not None and not pd.isna(white_line):
        white_line_distance = (white_line - current_price) / white_line * 100
        white_line_distance_ok = white_line_distance <= Config.WHITE_LINE_DISTANCE_THRESHOLD * 100
        print(f"价格与白线距离: {white_line_distance:.2f}% {'(符合做空条件)' if white_line_distance_ok else '(超出做空阈值)'}")
    else:
        print("白线不存在或为空，无法计算与白线的距离")
        
    # 检查价格与红线的距离（用于做多）
    if red_line is not None and not pd.isna(red_line):
        red_line_distance = (current_price - red_line) / red_line * 100
        red_line_distance_ok = red_line_distance <= Config.RED_LINE_DISTANCE_THRESHOLD * 100
        print(f"价格与红线距离: {red_line_distance:.2f}% {'(符合做多条件)' if red_line_distance_ok else '(超出做多阈值)'}")
    else:
        print("红线不存在或为空，无法计算与红线的距离")
        
    return red_line_distance_ok, white_line_distance_ok

def check_and_print_line_distance(current_price, white_line, red_line):
    """
    检查并打印价格与红白线的距离
    
    参数:
    current_price: 当前价格
    white_line: 白线值
    red_line: 红线值
    
    返回:
    (red_line_distance_ok, white_line_distance_ok): 距离是否符合要求
    """
    red_line_distance_ok = True
    white_line_distance_ok = True
    
    # 减少调试信息输出
    # print("\n=== 价格与红白线距离检查 ===")
    # print(f"当前价格: {current_price}")
    # 
    # # 检查价格与白线的距离（用于做空）
    # if white_line is not None and not pd.isna(white_line):
    #     white_line_distance = (white_line - current_price) / white_line * 100
    #     white_line_distance_ok = white_line_distance <= Config.WHITE_LINE_DISTANCE_THRESHOLD * 100
    #     print(f"白线价格: {white_line}")
    #     print(f"价格与白线距离: {white_line_distance:.2f}% {'(符合做空条件)' if white_line_distance_ok else '(超出做空阈值)'}")
    #     if not white_line_distance_ok:
    #         print(f"警告: 价格与白线距离超过{Config.WHITE_LINE_DISTANCE_THRESHOLD * 100:.2f}%阈值，建议等待反弹后再考虑做空")
    # else:
    #     print("白线不存在或为空，无法计算距离")
    # 
    # # 检查价格与红线的距离（用于做多）
    # if red_line is not None and not pd.isna(red_line):
    #     red_line_distance = (current_price - red_line) / red_line * 100
    #     red_line_distance_ok = red_line_distance <= Config.RED_LINE_DISTANCE_THRESHOLD * 100
    #     print(f"红线价格: {red_line}")
    #     print(f"价格与红线距离: {red_line_distance:.2f}% {'(符合做多条件)' if red_line_distance_ok else '(超出做多阈值)'}")
    #     if not red_line_distance_ok:
    #         print(f"警告: 价格与红线距离超过{Config.RED_LINE_DISTANCE_THRESHOLD * 100:.2f}%阈值，建议等待回调后再考虑做多")
    # else:
    #     print("红线不存在或为空，无法计算距离")
    
    return red_line_distance_ok, white_line_distance_ok

def enhanced_stop_loss_check(ContextInfo, current_price):
    """
    增强版止损检查
    
    参数:
    ContextInfo: 上下文信息
    current_price: 当前价格
    
    返回:
    should_stop: 是否应该止损
    stop_price: 止损价格
    stop_reason: 止损原因
    """
    try:
        # ===== 新增：开仓ATR止损保护期逻辑 =====
        protect_seconds = getattr(Config, 'ATR_STOP_PROTECT_MINUTES', 7) * 60
        now = time.time()
        if hasattr(g, 'open_time') and g.open_time > 0:
            if now - g.open_time < protect_seconds:
                skip_atr_stop = True
            else:
                skip_atr_stop = False
        else:
            skip_atr_stop = False
        price_1m = ContextInfo.get_market_data(['open', 'high', 'low', 'close', 'volume'], 
                                           [g.code], 
                                           period='1m',
                                           count=20)
        price_5m = ContextInfo.get_market_data(['open', 'high', 'low', 'close', 'volume'], 
                                           [g.code], 
                                           period='5m',
                                           count=10)
        white_line_5m, red_line_5m = calculate_red_white_lines_exact(price_5m)
        # ====== 计算三重止损线 ======
        # 1. 红线止损
        red_line = None
        if red_line_5m.iloc[-1] is not None and not pd.isna(red_line_5m.iloc[-1]):
            red_line = red_line_5m.iloc[-1]
        # 2. ATR止损
        atr = calculate_atr(ContextInfo, period=10)
        atr_stop_multiple = getattr(Config, 'ATR_STOP_MULTIPLE', 1.0)
        atr_stop_price = g.open_price - atr * atr_stop_multiple
        # 3. 最大亏损止损
        max_loss_price = None
        if getattr(Config, 'ENABLE_MAX_LOSS_STOP', True):
            max_loss_points = getattr(Config, 'MAX_LOSS_POINTS', 15)
            max_loss_price = g.open_price - max_loss_points
        # ====== 新增：近距离只用红线止损 ======
        no_atr_stop = False
        if red_line is not None and g.open_price > 0:
            red_line_distance = abs(g.open_price - red_line) / red_line
            if red_line_distance < getattr(Config, 'NO_ATR_STOP_DISTANCE', 0.003):
                no_atr_stop = True
        # ====== 取三者中价格最高的那一条 ======
        stop_candidates = []
        stop_reasons = []
        if red_line is not None:
            stop_candidates.append(red_line)
            stop_reasons.append(f'价格跌破红线({red_line:.2f})')
        # ===== 新增：保护期内或近距离不加入ATR止损 =====
        if not skip_atr_stop and not no_atr_stop and atr is not None and not pd.isna(atr):
            stop_candidates.append(atr_stop_price)
            stop_reasons.append(f'价格跌破ATR止损线({atr_stop_price:.2f})')
        if max_loss_price is not None:
            stop_candidates.append(max_loss_price)
            stop_reasons.append(f'价格跌破最大亏损止损线({max_loss_price:.2f})')
        if not stop_candidates:
            return False, None, None
        stop_line = max(stop_candidates)
        stop_reason = stop_reasons[stop_candidates.index(stop_line)]
        # ====== 成交量确认 ======
        volume_confirmed = False
        if len(price_1m) >= 3:
            volume_ratio = price_1m['volume'].iloc[-1] / max(price_1m['volume'].iloc[-2], 1)
            if volume_ratio > 1.2:
                volume_confirmed = True
        if current_price < stop_line and volume_confirmed:
            return True, stop_line, stop_reason
        return False, None, None
    except Exception as e:
        print(f"增强版止损检查出错: {str(e)}")
        return False, None, None

def real_time_stop_loss_monitor(ContextInfo):
    """
    实时止损监控
    
    参数:
    ContextInfo: 上下文信息
    """
    try:
        # 获取当前持仓
        if g.position != "long":
            return
            
        # 获取当前价格
        current_price = ContextInfo.get_market_data(['close'], 
                                                [g.code], 
                                                period='1m',
                                                count=1)['close'].iloc[-1]
        
        # 执行增强版止损检查
        should_stop, stop_price, stop_reason = enhanced_stop_loss_check(ContextInfo, current_price)
        
        if should_stop:
            print(f"触发止损: {stop_reason}, 价格={stop_price}")
            
            # 执行止损
            passorder(7, 1101, account, g.code, 14, 0, g.position_size, '', 1, '多单止损', ContextInfo)
            log_trade("多单止损", current_price)
            
            # 更新持仓状态
            g.position = "none"
            g.position_size = 0
            sync_position_status(ContextInfo)
            
            # 记录平仓时间
            g.last_close_time = time.time()
            
    except Exception as e:
        print(f"实时止损监控出错: {str(e)}")

def check_stop_loss_and_reverse(ContextInfo, current_price, last_position_type=None):
    try:
        # 减少调试信息输出
        # print("\n=== 检查止损和反向开仓 ===")
        # print(f"当前持仓: {g.position}")
        # print(f"当前价格: {current_price}")
        
        # 获取5分钟K线数据
        price_5m = ContextInfo.get_market_data(['open', 'high', 'low', 'close', 'volume'], 
                                           [g.code], 
                                           period='5m',
                                           count=10)
        
        # 计算红白线
        white_line_5m, red_line_5m = calculate_red_white_lines_exact(price_5m)
        
        # 多单止损并反手做空
        if g.position == "long":
            # 检查是否跌破红线
            red_line_broken = False
            
            # 情况1：红线消失且价格低于前一根红线
            if red_line_5m.iloc[-2] is not None and not pd.isna(red_line_5m.iloc[-2]) and (red_line_5m.iloc[-1] is None or pd.isna(red_line_5m.iloc[-1])):
                if price_5m['close'].iloc[-1] < red_line_5m.iloc[-2]:
                    red_line_broken = True
                    print("多单止损信号：检测到跌破红线支撑（红线消失）")
            
            # 情况2：价格低于当前红线
            elif red_line_5m.iloc[-1] is not None and not pd.isna(red_line_5m.iloc[-1]):
                if price_5m['close'].iloc[-1] < red_line_5m.iloc[-1]:
                    red_line_broken = True
                    print(f"多单止损信号：检测到跌破红线支撑（价格{price_5m['close'].iloc[-1]}低于红线{red_line_5m.iloc[-1]}）")
            
            if red_line_broken:
                # 1. 先平多仓
                last_position_type = g.position
                passorder(7, 1101, account, g.code, 14, 0, g.position_size, '', 1, '多单止损', ContextInfo)
                log_trade("多单止损", current_price)
                
                # 记录平仓时间
                g.last_close_time = time.time()
                
                # 2. 直接开空仓
                passorder(3, 1101, account, g.code, 14, 0, Config.TRADE_HANDS, '', 1, '反手开空', ContextInfo)
                log_trade("反手开空", current_price)
                
                # 3. 更新状态
                g.position = "short"
                g.buy_short += 1
                g.trace_time_short = time.time()
                g.hold_price = current_price
                g.open_price = current_price
                g.position_size = Config.TRADE_HANDS
                g.sysid = {}
                
                # 4. 重置止盈标记
                g.profit_taken_1_short = False
                g.profit_taken_2_short = False
                g.profit_taken_3_short = False
                
                # 在触发止损和反向开仓时添加更详细的日志
                print(f"触发止损和反向开仓:")
                print(f"- 原持仓方向: long")
                print(f"- 新持仓方向: short")
                print(f"- 开仓价格: {current_price}")
                
                return True
                
        # 空单止损并反手做多
        elif g.position == "short":
            # 检查是否突破白线
            white_line_broken = False
            
            # 情况1：白线消失且价格高于前一根白线
            if white_line_5m.iloc[-2] is not None and not pd.isna(white_line_5m.iloc[-2]) and (white_line_5m.iloc[-1] is None or pd.isna(white_line_5m.iloc[-1])):
                if price_5m['close'].iloc[-1] > white_line_5m.iloc[-2]:
                    white_line_broken = True
                    print("空单止损信号：检测到突破白线压力（白线消失）")
            
            # 情况2：价格高于当前白线
            elif white_line_5m.iloc[-1] is not None and not pd.isna(white_line_5m.iloc[-1]):
                if price_5m['close'].iloc[-1] > white_line_5m.iloc[-1]:
                    white_line_broken = True
                    print(f"空单止损信号：检测到突破白线压力（价格{price_5m['close'].iloc[-1]}高于白线{white_line_5m.iloc[-1]}）")
            
            if white_line_broken:
                # 1. 先平空仓
                last_position_type = g.position
                passorder(9, 1101, account, g.code, 14, 0, g.position_size, '', 1, '空单止损', ContextInfo)
                log_trade("空单止损", current_price)
                
                # 记录平仓时间
                g.last_close_time = time.time()
                
                # 2. 直接开多仓
                passorder(0, 1101, account, g.code, 14, 0, Config.TRADE_HANDS, '', 1, '反手开多', ContextInfo)
                log_trade("反手开多", current_price)
                
                # 3. 更新状态
                g.position = "long"
                g.buy_long += 1
                g.trace_time_long = time.time()
                g.hold_price = current_price
                g.open_price = current_price
                g.position_size = Config.TRADE_HANDS
                g.sysid = {}
                
                # 4. 重置止盈标记
                g.profit_taken_1 = False
                g.profit_taken_2 = False
                g.profit_taken_3 = False
                
                # 在触发止损和反向开仓时添加更详细的日志
                print(f"触发止损和反向开仓:")
                print(f"- 原持仓方向: short")
                print(f"- 新持仓方向: long")
                print(f"- 开仓价格: {current_price}")
                
                return True
                
        return False
        
    except Exception as e:
        print(f"止损和反向开仓检查出错: {str(e)}")
        return False

# 新增获取参数的函数
def get_atr_stop_multiple(code):
    prefix = code[:2].upper() if code else ''
    return Config.ATR_STOP_MULTIPLE_MAP.get(prefix, Config.ATR_STOP_MULTIPLE)

def get_min_stop_distance(code):
    prefix = code[:2].upper() if code else ''
    return Config.MIN_STOP_DISTANCE_MAP.get(prefix, Config.MIN_STOP_DISTANCE)

# ====== 新增获取分品种止盈参数的函数 ======
def get_take_profit_atr_1(code):
    prefix = code[:2].upper() if code else ''
    return Config.TAKE_PROFIT_ATR_1_MAP.get(prefix, Config.DEFAULT_TAKE_PROFIT_ATR_1)

def get_min_take_profit_points(code):
    prefix = code[:2].upper() if code else ''
    return Config.MIN_TAKE_PROFIT_POINTS_MAP.get(prefix, Config.DEFAULT_MIN_TAKE_PROFIT_POINTS)
# ====== End 新增函数 ======

